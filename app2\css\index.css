/* Index page specific styles */

/* Hero section */
.hero {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 60px 0;
    margin-bottom: 48px;
    border-radius: 20px;
    margin: 24px 16px 48px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.hero-left {
    text-align: left;
}

.hero-right {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 600;
    margin-bottom: 16px;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
    margin-bottom: 32px;
}

.hero-recommendations {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 24px;
    margin-top: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.recommendations-title {
    font-size: 1.4rem;
    font-weight: 500;
    margin-bottom: 8px;
    color: #fff;
}

.recommendations-text {
    font-size: 1rem;
    opacity: 0.8;
    margin: 0;
}

.hero-apps {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    max-width: 300px;
}

.hero-app-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 120px;
}

.hero-app-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.hero-app-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.hero-app-name {
    font-size: 0.85rem;
    font-weight: 500;
    color: white;
    text-align: center;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.hero-app-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.9);
}

/* Featured Swiper Section */
.featured-swiper-section {
    margin-bottom: 48px;
    padding: 0 16px;
}

.featured-swiper-section .section-header {
    text-align: center;
    margin-bottom: 32px;
}

.featured-swiper-section .section-title {
    color: white;
    font-size: 2rem;
    margin-bottom: 8px;
}

.featured-swiper-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
}

.featured-swiper {
    padding: 20px 0 60px;
}

.featured-swiper .swiper-slide {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.featured-swiper .swiper-slide:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.featured-app-content {
    text-align: center;
}

.featured-app-icon {
    width: 80px;
    height: 80px;
    border-radius: 18px;
    margin: 0 auto 16px;
    display: block;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.featured-app-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #202124;
    margin-bottom: 8px;
}

.featured-app-developer {
    color: #5f6368;
    font-size: 0.95rem;
    margin-bottom: 12px;
}

.featured-app-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.featured-app-description {
    color: #5f6368;
    font-size: 0.9rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.swiper-pagination-bullet {
    background: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

.swiper-pagination-bullet-active {
    background: white;
}

.swiper-button-next,
.swiper-button-prev {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 44px;
    height: 44px;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 18px;
}

/* Category sections */
.category-section {
    margin-bottom: 48px;
    padding: 0 16px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 500;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.2s ease;
}

.view-all-btn:hover {
    background-color: #667eea;
}

/* Business Apps - Featured Grid Layout */
.business-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.business-app-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.business-app-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.business-app-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.business-app-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    object-fit: cover;
}

.business-app-info h3 {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 4px;
    color: #202124;
}

.business-app-info p {
    color: #5f6368;
    font-size: 0.9rem;
}

.business-app-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.business-app-description {
    color: #5f6368;
    font-size: 0.9rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Weather Apps - Horizontal Scroll Layout */
.apps-scroll-container {
    position: relative;
    overflow: hidden;
}

.weather-scroll {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: #dadce0 transparent;
}

.weather-scroll::-webkit-scrollbar {
    height: 6px;
}

.weather-scroll::-webkit-scrollbar-track {
    background: transparent;
}

.weather-scroll::-webkit-scrollbar-thumb {
    background: #dadce0;
    border-radius: 3px;
}

.weather-app-card {
    flex: 0 0 200px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.weather-app-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.weather-app-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    object-fit: cover;
    margin-bottom: 12px;
}

.weather-app-card h3 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 4px;
    color: #202124;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.weather-app-card p {
    color: #5f6368;
    font-size: 0.85rem;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Utilities Apps - Compact List Layout */
.utilities-list {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.utilities-app-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.utilities-app-item:last-child {
    border-bottom: none;
}

.utilities-app-item:hover {
    background-color: #f8f9fa;
}

.utilities-app-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
}

.utilities-app-info {
    flex: 1;
}

.utilities-app-info h3 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 2px;
    color: #202124;
}

.utilities-app-info p {
    color: #5f6368;
    font-size: 0.85rem;
}

.utilities-app-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #5f6368;
    font-size: 0.85rem;
}

/* Travel Apps - Card Layout */
.travel-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
    gap: 24px;
    justify-items: center;
}

.travel-app-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.3);
    width: 280px;
    height: 240px;
    display: flex;
    flex-direction: column;
}

.travel-app-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.travel-app-image-container {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px 12px 0 0;
}

.travel-app-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.travel-app-content {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.travel-app-content h3 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 4px;
    color: #202124;
}

.travel-app-content p {
    color: #5f6368;
    font-size: 0.85rem;
    margin-bottom: 8px;
}

.travel-app-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Sports Apps - Banner Layout */
.sports-banner {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.sports-app-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 24px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sports-app-banner:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.sports-app-banner::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.sports-banner-content {
    position: relative;
    z-index: 1;
}

.sports-app-banner h3 {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.sports-app-banner p {
    opacity: 0.9;
    font-size: 0.9rem;
    margin-bottom: 12px;
}

.sports-banner-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sports-banner-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .hero {
        padding: 32px 0;
        margin-bottom: 32px;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 24px;
        text-align: center;
    }

    .hero-left {
        text-align: center;
    }

    .hero-right {
        justify-content: center;
    }

    .hero-apps {
        grid-template-columns: repeat(2, 1fr);
        margin: 0 auto;
    }

    .hero-title {
        font-size: 2rem;
    }

    .category-section {
        margin-bottom: 32px;
    }

    .section-header {
        margin-bottom: 16px;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .business-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .weather-app-card {
        flex: 0 0 160px;
    }

    .travel-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
    }

    .sports-banner {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-apps {
        gap: 12px;
    }

    .hero-app-item {
        min-width: 100px;
        padding: 12px;
    }

    .hero-app-icon {
        width: 40px;
        height: 40px;
    }

    .hero-app-name {
        font-size: 0.8rem;
    }

    .weather-app-card {
        flex: 0 0 140px;
    }

    .sports-app-banner {
        padding: 20px;
    }
}
