/* Details page specific styles */

/* App header */
.app-header {
    position: relative;
    margin-bottom: 32px;
    padding: 60px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    overflow: hidden;
}

.app-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    backdrop-filter: blur(10px);
}

.app-header-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-link:hover {
    color: white;
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-current {
    color: rgba(255, 255, 255, 0.7);
}

.app-info {
    display: flex;
    gap: 24px;
    align-items: center;
}

.app-icon-container {
    flex-shrink: 0;
}

.app-icon {
    width: 96px;
    height: 96px;
    border-radius: 20px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.app-details {
    flex: 1;
    min-width: 0;
}

.app-category-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
}

.app-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-developer {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 12px;
    text-decoration: none;
}

.app-developer:hover {
    text-decoration: underline;
}

.app-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.rating-value {
    font-weight: 500;
    color: white;
}

.rating-count {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.app-category {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.category-label {
    font-weight: 500;
}

.category-value {
    color: rgba(255, 255, 255, 0.9);
}

.app-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.install-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #1a73e8;
    color: white;
    border: none;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.install-btn:hover {
    background: #1557b0;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
}

.wishlist-btn,
.share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: white;
    border: 1px solid #dadce0;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #5f6368;
}

.wishlist-btn:hover,
.share-btn:hover {
    background: #f8f9fa;
    border-color: #1a73e8;
    color: #1a73e8;
}

.wishlist-btn.active {
    background: #ff6b6b;
    border-color: #ff6b6b;
    color: white;
}

.wishlist-btn.active:hover {
    background: #ff5252;
    border-color: #ff5252;
    color: white;
}

/* App content */
.app-content {
    padding: 0 16px;
}

.content-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
    /* background: rgba(255, 255, 255, 0.1); */
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 32px;
    /* border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); */
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.sidebar {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.related-apps-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.sidebar-title {
    font-size: 1.2rem;
    font-weight: 500;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.related-apps-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.related-app-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border-radius: 12px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid transparent;
}

.related-app-item:hover {
    background: rgba(26, 115, 232, 0.05);
    border-color: rgba(26, 115, 232, 0.2);
}

.related-app-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    object-fit: cover;
    flex-shrink: 0;
}

.related-app-info {
    flex: 1;
    min-width: 0;
}

.related-app-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-app-developer {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 6px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-app-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

.section-title {
    font-size: 1.3rem;
    font-weight: 500;
    color: white;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Screenshots section */
.screenshots-container {
    position: relative;
    overflow: hidden;
    max-width: 100%;
    width: 100%;
}

.screenshots-scroll {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: #dadce0 transparent;
    max-width: 800px;
    width: 100%;
}

.screenshots-scroll::-webkit-scrollbar {
    height: 6px;
}

.screenshots-scroll::-webkit-scrollbar-track {
    background: transparent;
}

.screenshots-scroll::-webkit-scrollbar-thumb {
    background: #dadce0;
    border-radius: 3px;
}

.screenshot-item {
    flex: 0 0 auto;
    cursor: pointer;
    transition: transform 0.2s ease;
    max-width: 180px;
}

.screenshot-item:hover {
    transform: scale(1.02);
}

.screenshot-img {
    height: 280px;
    width: 160px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: cover;
}

/* Description section */
.description-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.description-text {
    color: white;
    line-height: 1.6;
    font-size: 0.95rem;
}

.description-text.collapsed {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.read-more-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    cursor: pointer;
    margin-top: 12px;
    padding: 0;
    text-decoration: underline;
}

.read-more-btn:hover {
    color: white;
}

/* App info section */
.app-info-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.info-value {
    color: white;
    font-size: 0.9rem;
}

/* Ratings section */
.ratings-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.ratings-overview {
    display: flex;
    gap: 32px;
    align-items: center;
}

.rating-summary {
    text-align: center;
}

.rating-score {
    font-size: 3rem;
    font-weight: 300;
    color: white;
    margin-bottom: 8px;
}

.rating-stars-large {
    display: flex;
    gap: 4px;
    justify-content: center;
    margin-bottom: 8px;
}

.rating-stars-large .star {
    width: 20px;
    height: 20px;
}

.rating-total {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.rating-breakdown {
    flex: 1;
    max-width: 300px;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.rating-label {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    width: 8px;
}

.bar-container {
    flex: 1;
    height: 8px;
    background: #f1f3f4;
    border-radius: 4px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: #fbbc04;
    transition: width 0.3s ease;
}

/* Responsive design */
@media (max-width: 1024px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .sidebar {
        position: static;
        order: -1;
    }

    .related-apps-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
    }
}

@media (max-width: 768px) {
    .content-layout {
        padding: 0;
    }

    .app-header {
        padding: 40px 0;
        margin-bottom: 24px;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
    }

    .app-header-content {
        padding: 0 16px;
    }

    .app-info {
        flex-direction: column;
        gap: 16px;
        align-items: center;
        text-align: center;
    }

    .app-icon {
        width: 80px;
        height: 80px;
    }

    .app-title {
        font-size: 2rem;
    }

    .app-actions {
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }

    .app-content {
        padding: 0 8px;
    }

    .content-layout {
        gap: 24px;
        display: flex;
        flex-direction: column-reverse;
    }

    .main-content {
        gap: 24px;
    }

    .section-title {
        font-size: 1.2rem;
    }

    .screenshot-img {
        height: 250px;
        width: 140px;
    }

    .screenshots-scroll {
        max-width: calc(100vw - 32px);
    }

    .description-content,
    .app-info-section,
    .ratings-section,
    .related-apps-section {
        padding: 20px;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .ratings-overview {
        display: flex;
        gap: 32px;
        align-items: center;
    }

    .related-apps-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .app-title {
        font-size: 1.4rem;
    }

    .app-icon {
        width: 72px;
        height: 72px;
    }

    .screenshot-img {
        height: 200px;
    }

    .description-content,
    .app-info-section,
    .ratings-section {
        padding: 16px;
    }

    .rating-score {
        font-size: 2.5rem;
    }

    .ratings-overview {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .rating-breakdown {
        max-width: 100%;
        width: 100%;
    }
}