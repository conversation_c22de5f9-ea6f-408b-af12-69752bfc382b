{"isGameCenterEnabled": false, "artistViewUrl": "https://apps.apple.com/us/developer/bjo<PERSON>-j<PERSON><PERSON>/id590354152?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/63/2d/c1/632dc13c-3b8a-ca07-e702-654565444b37/AppIcon_NEW-0-0-1x_U007epad-0-1-sRGB-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/63/2d/c1/632dc13c-3b8a-ca07-e702-654565444b37/AppIcon_NEW-0-0-1x_U007epad-0-1-sRGB-85-220.png/100x100bb.jpg", "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "Watch4-Watch4", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "features": ["iosUniversal"], "advisories": [], "kind": "software", "screenshotUrls": [], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/6d/56/6c/6d566ccc-bcd8-e723-58be-2f02e5dd5535/ea2b96fb-e063-4d43-88ed-61cf920d04da_iPad-Pro-Appstore_main_low.jpg/576x768bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/8a/16/d0/8a16d087-9331-6aee-81b3-a89f499f2196/f63f3f88-16e2-4ec3-8491-4670708aedea_iPad-Pro-Appstore_main_high.jpg/576x768bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/d6/fd/ab/d6fdabb7-e376-f9c3-e560-2b4986644f1a/66b9b221-9826-412f-a43b-a2575255d4e4_iPad-Pro-Appstore_forecast.jpg/576x768bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/df/45/2d/df452d74-f2fa-90d5-90e6-ae087347e2b8/b75e7d67-c732-4397-a895-a59a342bf325_iPad-Pro-Appstore_my_skin.jpg/576x768bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/1c/dc/49/1cdc49e1-ed60-e8df-1d70-6d2775043ac3/a958b439-c58e-40e5-b5b5-98cd3390ab80_iPad-Pro-DarkMode.jpg/576x768bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/dc/6a/75/dc6a752a-8f83-708f-f555-ffb182dcc528/45109c24-27d2-4a34-87f6-68f97f8cb0b2_iPad-Pro-Appstore_widget.jpg/576x768bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/8f/24/08/8f24082d-7430-a1f5-ea61-5dc432a46191/c7e3c57b-2cd3-4168-86fd-e42717f7136d_iPad-Pro-Appstore_places.jpg/576x768bb.jpg"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/63/2d/c1/632dc13c-3b8a-ca07-e702-654565444b37/AppIcon_NEW-0-0-1x_U007epad-0-1-sRGB-85-220.png/512x512bb.jpg", "userRatingCountForCurrentVersion": 13129, "minimumOsVersion": "16.4", "averageUserRating": 4.76227, "contentAdvisoryRating": "4+", "trackCensoredName": "UV Index Widget - Worldwide", "trackViewUrl": "https://apps.apple.com/us/app/uv-index-widget-worldwide/id**********?uo=4", "artistId": 590354152, "artistName": "<PERSON><PERSON><PERSON>", "genres": ["Weather", "Health & Fitness"], "price": 0, "trackId": **********, "trackName": "UV Index Widget - Worldwide", "bundleId": "UV-Index-Widget", "genreIds": ["6001", "6013"], "releaseNotes": "Minor bugfixes and improvements", "releaseDate": "2016-05-03T15:29:09Z", "primaryGenreName": "Weather", "primaryGenreId": 6001, "isVppDeviceBasedLicensingEnabled": true, "sellerName": "<PERSON><PERSON><PERSON>", "currentVersionReleaseDate": "2025-07-02T19:49:19Z", "version": "11.2.3", "wrapperType": "software", "currency": "USD", "description": "UV Index Widget - Worldwide brings one of the most useful pieces of weather data, the UV Index, front and center. Use this app to track your UV exposure, help plan your day outside, and avoid burning. Perfect for planning your tanning routine.\n \nUV Index Widget - Worldwide is a convenient app that lets you monitor the Sun’s radiation levels anywhere on Earth. It shows the current UV Index, today’s peak forecast, and a 7-day UV forecast. You can add the UV Index to your Home Screen via widgets, receive alerts when UV Index reaches specific levels, and automatically log the data to your Apple Health app.\n\nThe app instantly displays your current UV Index (UVI) in an easy-to-understand format. Just tap and hold to view today's highest UVI reading. You can also check the sunrise and sunset times, along with the moment when the UV Index peaks. \n\n7-day hour-by-hour forecast:\nThe Forecast tab provides an hour-by-hour UVI prediction for the next 7 days.\n\nMy Skin:\nIn the My Skin section, select your skin type for tailored safety tips and recommendations. You can also monitor your sun exposure and receive alerts when there’s a high risk of sunburn. Additionally, the app tracks your Vitamin D production from sunlight and automatically syncs the data to your Apple Health app.\n\nPlaces:\nIn the Places tab, you can select any location around the world to see its current UV radiation and a 7-day forecast. It's a great tool for checking the UV levels at your travel destination before you head out.\n\nWidget:\nThe app offers a variety of Home Screen widgets and Lock Screen Widgets, designed for both your iPhone and Apple Watch. Choose from multiple options to customize your experience on the Home Screen, Today view and Apple Watch.\n\nNotifications:\nThe app alerts you when the UV Index exceeds your specified threshold, sending a notification whenever your area's UV levels surpass that mark. It also warns you when you're at high risk of sunburn and reminds you to reapply sunscreen.\n\nApple Health app:\nThe app automatically logs your location's hourly UV Index and the vitamin D generated from sun exposure into the Apple Health app. Open Apple Health to review your UV exposure history and vitamin D data.\n\nApple Watch:\nMonitor the UV Index directly from your watch. Tap and hold to view today's peak UVI, just like in the iOS app. The watch app also features a My Skin section and a 7-day hourly forecast. Additionally, you can always check the UVI right on your favorite watch face using the included UVI complications.\n\nThe app will work anywhere in the world, and is a perfect companion whether you are at home or travelling.\n\n---\n\nInformation about UV Index Widget - Worldwide subscription:\n-At confirmation of purchase, or following the completion of a free-trial period, payment will be charged to your iTunes account.\n- Yearly subscriptions are available.\n- Payment will be charged to your iTunes account at confirmation of purchase.\n- Your subscription will automatically renew unless auto-renew is turned off at least 24-hours before the end of the current subscription period.\n- Your account will be charged for renewal within 24-hours prior to the end of the current subscription period. Automatic renewals will cost the same price you were originally charged for the subscription.\n- You can manage your subscriptions and turn off auto-renewal by going to your Account Settings on the App Store after purchase.\n-Any unused portion of a free trial period, if offered, will be forfeited when the user purchases a subscription to that publication, where applicable\n\nTerms of Use:\nhttps://uv-index-worldwide.web.app/tos_uvindex.html\n\n---\n\nThe UV Index scale used in this app conforms with international guidelines for UVI reporting established by the World Health Organization.", "languageCodesISO2A": ["EN", "FR", "DE", "JA", "ZH", "ES"], "fileSizeBytes": "********", "formattedPrice": "Free", "trackContentRating": "4+", "averageUserRatingForCurrentVersion": 4.76227, "sellerUrl": "https://uv-index-worldwide.web.app", "userRatingCount": 13129}