// Details page specific JavaScript

// App details state
let currentAppId = null;
let appDetails = null;
let isDescriptionExpanded = false;

// Initialize details page
document.addEventListener('DOMContentLoaded', async function () {
    try {
        // Initialize data
        const dataLoaded = await DataAPI.initializeData();
        if (dataLoaded) {
            initializeDetailsPage();
        } else {
            showErrorMessage('Failed to load app data. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error initializing details page:', error);
        showErrorMessage('Something went wrong. Please refresh the page.');
    }
});

// Initialize details page functionality
async function initializeDetailsPage() {
    // Get app ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    currentAppId = urlParams.get('id');

    if (!currentAppId) {
        showErrorMessage('App not found. Please check the URL.');
        return;
    }

    try {
        // Load app details
        await loadAppDetails();
    } catch (error) {
        console.error('Error loading app details:', error);
        showErrorMessage('Failed to load app details. Please try again.');
    }
}

// Load app details from API
async function loadAppDetails() {
    try {
        showLoading();

        // Get basic app info from main data
        const basicApp = DataAPI.getAppById(currentAppId);
        if (!basicApp) {
            throw new Error('App not found in main data');
        }

        // Load detailed app information
        appDetails = await DataAPI.loadAppDetails(currentAppId);

        // Merge basic and detailed info
        const fullAppData = { ...basicApp, ...appDetails };

        // Display app details
        displayAppDetails(fullAppData);

        // Initialize wishlist button state
        initializeWishlistButton();

        hideLoading();

    } catch (error) {
        hideLoading();
        console.error('Error loading app details:', error);

        // Try to show basic info if detailed info fails
        const basicApp = DataAPI.getAppById(currentAppId);
        if (basicApp) {
            displayBasicAppDetails(basicApp);
        } else {
            throw error;
        }
    }
}

// Display full app details
function displayAppDetails(app) {
    // Update page title
    document.title = `${app.trackName || app.appName} - Play Store`;

    // Update breadcrumb
    updateBreadcrumb(app);

    // Update app header
    updateAppHeader(app);

    // Update header background
    updateHeaderBackground(app);

    // Update screenshots
    updateScreenshots(app);

    // Update description
    updateDescription(app);

    // Update app info
    updateAppInfo(app);

    // Update ratings
    updateRatings(app);

    // Load related apps
    loadRelatedApps(app);
}
localStorage
// Display basic app details (fallback)
function displayBasicAppDetails(app) {
    document.title = `${app.appName} - Play Store`;

    updateBreadcrumb(app);
    updateBasicAppHeader(app);
    updateBasicDescription(app);
    updateBasicAppInfo(app);
    updateBasicRatings(app);

    // Hide screenshots section if no detailed data
    const screenshotsSection = document.getElementById('screenshotsSection');
    if (screenshotsSection) {
        screenshotsSection.style.display = 'none';
    }
}

// Update header background with first screenshot
function updateHeaderBackground(app) {
    const headerElement = document.getElementById('appHeader');
    if (!headerElement) return;

    // Use first screenshot as background, fallback to app icon
    const backgroundImage = (app.screenshotUrls && app.screenshotUrls.length > 0)
        ? app.screenshotUrls[0]
        : app.artworkUrl512 || app.icon;

    if (backgroundImage) {
        headerElement.style.backgroundImage = `url(${backgroundImage})`;
    }
}

// Update breadcrumb navigation
function updateBreadcrumb(app) {
    const categoryBreadcrumb = document.getElementById('categoryBreadcrumb');
    const appBreadcrumb = document.getElementById('appBreadcrumb');

    const category = app.primaryGenreName || app.type || 'Apps';

    if (categoryBreadcrumb) {
        categoryBreadcrumb.textContent = category;
        categoryBreadcrumb.href = `../types/type.html?category=${encodeURIComponent(category)}`;
    }

    if (appBreadcrumb) {
        appBreadcrumb.textContent = app.trackName || app.appName;
    }
}

// Update app header section
function updateAppHeader(app) {
    // App icon
    const appIcon = document.getElementById('appIcon');
    if (appIcon) {
        appIcon.src = app.artworkUrl512 || app.icon;
        appIcon.alt = app.trackName || app.appName;
    }

    // App title
    const appTitle = document.getElementById('appTitle');
    if (appTitle) {
        appTitle.textContent = app.trackName || app.appName;
    }

    // Developer
    const appDeveloper = document.getElementById('appDeveloper');
    if (appDeveloper) {
        appDeveloper.textContent = app.artistName || app.publisher;
    }

    // Rating
    updateRatingDisplay(app);

    // Category
    const appCategory = document.getElementById('appCategory');
    if (appCategory) {
        appCategory.textContent = app.primaryGenreName || app.type;
    }

    // Category badge
    const appCategoryBadge = document.getElementById('appCategoryBadge');
    if (appCategoryBadge) {
        appCategoryBadge.textContent = app.primaryGenreName || app.type;
    }
}

// Update basic app header (fallback)
function updateBasicAppHeader(app) {
    const appIcon = document.getElementById('appIcon');
    if (appIcon) {
        appIcon.src = app.icon;
        appIcon.alt = app.appName;
    }

    const appTitle = document.getElementById('appTitle');
    if (appTitle) {
        appTitle.textContent = app.appName;
    }

    const appDeveloper = document.getElementById('appDeveloper');
    if (appDeveloper) {
        appDeveloper.textContent = app.publisher;
    }

    updateBasicRatingDisplay(app);

    const appCategory = document.getElementById('appCategory');
    if (appCategory) {
        appCategory.textContent = app.type;
    }

    // Category badge for basic app
    const appCategoryBadge = document.getElementById('appCategoryBadge');
    if (appCategoryBadge) {
        appCategoryBadge.textContent = app.type;
    }
}

// Update rating display
function updateRatingDisplay(app) {
    const ratingStars = document.getElementById('ratingStars');
    const ratingValue = document.getElementById('ratingValue');
    const ratingCount = document.getElementById('ratingCount');

    const rating = app.averageUserRating || app.rating || 0;
    const reviewCount = app.userRatingCount || app.num || 0;

    if (ratingStars) {
        ratingStars.innerHTML = generateStars(rating, 20);
    }

    if (ratingValue) {
        ratingValue.textContent = rating.toFixed(1);
    }

    if (ratingCount) {
        ratingCount.textContent = `(${formatNumber(reviewCount)} reviews)`;
    }
}

// Update basic rating display (fallback)
function updateBasicRatingDisplay(app) {
    const ratingStars = document.getElementById('ratingStars');
    const ratingValue = document.getElementById('ratingValue');
    const ratingCount = document.getElementById('ratingCount');

    if (ratingStars) {
        ratingStars.innerHTML = generateStars(app.rating, 20);
    }

    if (ratingValue) {
        ratingValue.textContent = app.rating.toFixed(1);
    }

    if (ratingCount) {
        ratingCount.textContent = `(${formatNumber(app.num)} reviews)`;
    }
}

// Update screenshots section
function updateScreenshots(app) {
    const screenshotsContainer = document.getElementById('screenshotsScroll');

    if (!screenshotsContainer) return;

    const screenshots = app.screenshotUrls || [];

    if (screenshots.length === 0) {
        document.getElementById('screenshotsSection').style.display = 'none';
        return;
    }

    screenshotsContainer.innerHTML = screenshots.map((url, index) => `
        <div class="screenshot-item" onclick="openScreenshot('${url}')">
            <img src="${url}" alt="Screenshot ${index + 1}" class="screenshot-img"
                 onerror="this.parentElement.style.display='none'">
        </div>
    `).join('');
}

// Update description section
function updateDescription(app) {
    const descriptionText = document.getElementById('appDescription');
    const readMoreBtn = document.getElementById('readMoreBtn');

    if (!descriptionText) return;

    const description = app.description || 'No description available for this app.';

    descriptionText.innerHTML = description.replace(/\n/g, '<br>');

    // Show read more button if description is long
    if (description.length > 300) {
        descriptionText.classList.add('collapsed');
        if (readMoreBtn) {
            readMoreBtn.style.display = 'block';
        }
    }
}

// Update basic description (fallback)
function updateBasicDescription(app) {
    const descriptionText = document.getElementById('appDescription');

    if (descriptionText) {
        const description = app.subtitle || 'Discover this amazing app and enhance your mobile experience with its powerful features and intuitive design.';
        descriptionText.textContent = description;
    }
}

// Update app info section
function updateAppInfo(app) {
    const elements = {
        appVersion: app.version || 'Unknown',
        appSize: app.fileSizeBytes ? formatFileSize(parseInt(app.fileSizeBytes)) : 'Unknown',
        appUpdated: app.currentVersionReleaseDate ? formatDate(app.currentVersionReleaseDate) : formatDate(app.lastReleaseTime),
        appRequires: app.minimumOsVersion ? `iOS ${app.minimumOsVersion}+` : 'Unknown',
        appContentRating: app.contentAdvisoryRating || app.trackContentRating || 'Unknown',
        appDeveloperInfo: app.artistName || app.publisher || 'Unknown'
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// Update basic app info (fallback)
function updateBasicAppInfo(app) {
    const elements = {
        appVersion: 'Unknown',
        appSize: 'Unknown',
        appUpdated: formatDate(app.lastReleaseTime),
        appRequires: 'Unknown',
        appContentRating: 'Unknown',
        appDeveloperInfo: app.publisher
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// Update ratings section
function updateRatings(app) {
    const overallRating = document.getElementById('overallRating');
    const overallStars = document.getElementById('overallStars');
    const totalReviews = document.getElementById('totalReviews');

    const rating = app.averageUserRating || app.rating || 0;
    const reviewCount = app.userRatingCount || app.num || 0;

    if (overallRating) {
        overallRating.textContent = rating.toFixed(1);
    }

    if (overallStars) {
        overallStars.innerHTML = generateStars(rating, 24);
    }

    if (totalReviews) {
        totalReviews.textContent = `${formatNumber(reviewCount)} reviews`;
    }

    // Update rating breakdown (simplified)
    updateRatingBreakdown(rating);
}

// Update basic ratings (fallback)
function updateBasicRatings(app) {
    const overallRating = document.getElementById('overallRating');
    const overallStars = document.getElementById('overallStars');
    const totalReviews = document.getElementById('totalReviews');

    if (overallRating) {
        overallRating.textContent = app.rating.toFixed(1);
    }

    if (overallStars) {
        overallStars.innerHTML = generateStars(app.rating, 24);
    }

    if (totalReviews) {
        totalReviews.textContent = `${formatNumber(app.num)} reviews`;
    }

    updateRatingBreakdown(app.rating);
}

// Update rating breakdown bars
function updateRatingBreakdown(rating) {
    const bars = document.querySelectorAll('.rating-bar .bar-fill');

    // Simulate rating distribution based on overall rating
    const distribution = generateRatingDistribution(rating);

    bars.forEach((bar, index) => {
        const percentage = distribution[4 - index] || 0;
        bar.style.width = `${percentage}%`;
    });
}

// Generate simulated rating distribution
function generateRatingDistribution(rating) {
    // This is a simplified simulation
    const base = Math.max(0, rating - 2);
    return [
        Math.max(0, 10 - rating * 2), // 1 star
        Math.max(0, 15 - rating * 2), // 2 stars
        Math.max(0, 20 - rating * 2), // 3 stars
        Math.min(40, base * 8), // 4 stars
        Math.min(60, rating * 12) // 5 stars
    ];
}

// Toggle description expansion
function toggleDescription() {
    const descriptionText = document.getElementById('appDescription');
    const readMoreBtn = document.getElementById('readMoreBtn');

    if (!descriptionText || !readMoreBtn) return;

    isDescriptionExpanded = !isDescriptionExpanded;

    if (isDescriptionExpanded) {
        descriptionText.classList.remove('collapsed');
        readMoreBtn.textContent = 'Read less';
    } else {
        descriptionText.classList.add('collapsed');
        readMoreBtn.textContent = 'Read more';
    }
}

// App actions
function installApp() {
    // Use the detailed app data that's already loaded
    if (!appDetails) {
        alert('App details not loaded');
        return;
    }

    // Get the trackViewUrl from the detailed app info
    const trackViewUrl = appDetails.trackViewUrl;

    if (trackViewUrl) {
        // Open the trackViewUrl in a new tab
        window.open(trackViewUrl, '_blank');
    } else {
        alert('Download link not available');
    }
}

function toggleWishlist() {
    if (!currentAppId) {
        alert('App ID not found');
        return;
    }

    // Get current wishlist from localStorage
    let wishlist = getWishlist();

    // Check if app is already in wishlist
    const isInWishlist = wishlist.includes(currentAppId);

    if (isInWishlist) {
        // Remove from wishlist
        wishlist = wishlist.filter(id => id !== currentAppId);
    } else {
        // Add to wishlist
        wishlist.push(currentAppId);
    }

    // Save updated wishlist to localStorage
    saveWishlist(wishlist);

    // Update button appearance
    updateWishlistButton(!isInWishlist);
}

// Get wishlist from localStorage
function getWishlist() {
    try {
        const wishlist = localStorage.getItem('appWishlist');
        return wishlist ? JSON.parse(wishlist) : [];
    } catch (error) {
        console.error('Error reading wishlist from localStorage:', error);
        return [];
    }
}

// Save wishlist to localStorage
function saveWishlist(wishlist) {
    try {
        localStorage.setItem('appWishlist', JSON.stringify(wishlist));
    } catch (error) {
        console.error('Error saving wishlist to localStorage:', error);
    }
}

// Update wishlist button appearance
function updateWishlistButton(isInWishlist) {
    const wishlistBtn = document.querySelector('.wishlist-btn');
    if (!wishlistBtn) return;

    if (isInWishlist) {
        // App is in wishlist - show filled heart
        wishlistBtn.classList.add('active');
        wishlistBtn.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
            </svg>
            Remove from Wishlist
        `;
    } else {
        // App is not in wishlist - show outline heart
        wishlistBtn.classList.remove('active');
        wishlistBtn.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
            </svg>
            Add to Wishlist
        `;
    }
}

// Initialize wishlist button state when page loads
function initializeWishlistButton() {
    if (!currentAppId) return;

    const wishlist = getWishlist();
    const isInWishlist = wishlist.includes(currentAppId);
    updateWishlistButton(isInWishlist);
}

function shareApp() {
    if (navigator.share && appDetails) {
        navigator.share({
            title: appDetails.trackName || appDetails.appName,
            text: `Check out this amazing app: ${appDetails.trackName || appDetails.appName}`,
            url: window.location.href
        });
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('App link copied to clipboard!');
        });
    }
}

// Load related apps
function loadRelatedApps(app) {
    const container = document.getElementById('relatedAppsList');
    if (!container) return;
    let num = window.innerWidth < 768 ? 8 : 6;
    try {
        const category = app.primaryGenreName || app.type;
        const relatedApps = DataAPI.getRelatedApps(currentAppId, category, num);

        if (relatedApps.length === 0) {
            container.innerHTML = '<p style="color: #5f6368; text-align: center; padding: 20px;">No related apps found</p>';
            return;
        }

        container.innerHTML = relatedApps.map(relatedApp => `
            <div class="related-app-item" onclick="navigateToRelatedApp(${relatedApp.appId})">
                <img src="${relatedApp.icon}" alt="${relatedApp.appName}" class="related-app-icon"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
                <div class="related-app-info">
                    <div class="related-app-title">${relatedApp.appName}</div>
                    <div class="related-app-developer">${relatedApp.publisher}</div>
                    <div class="related-app-rating">
                        <div class="rating-stars">${generateStars(relatedApp.rating, 12)}</div>
                        <span>${relatedApp.rating.toFixed(1)}</span>
                    </div>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('Error loading related apps:', error);
        container.innerHTML = '<p style="color: #5f6368; text-align: center; padding: 20px;">Unable to load related apps</p>';
    }
}

// Navigate to related app
function navigateToRelatedApp(appId) {
    if (appId) {
        window.location.href = `detail.html?id=${appId}`;
    }
}

// Open screenshot in modal (future enhancement)
function openScreenshot(url) {
    // For now, just open in new tab
    window.open(url, '_blank');
}

// Show error message
function showErrorMessage(message) {
    const main = document.querySelector('.main .container');
    if (main) {
        main.innerHTML = `
            <div class="error-container" style="text-align: center; padding: 60px 20px;">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="color: #dadce0; margin-bottom: 16px;">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <h2 style="color: #202124; margin-bottom: 8px; font-weight: 400;">Oops! Something went wrong</h2>
                <p style="color: #5f6368; margin-bottom: 20px;">${message}</p>
                <button onclick="window.location.reload()" style="background: #1a73e8; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: 500;">
                    Refresh Page
                </button>
            </div>
        `;
    }
}

// Handle search form submission
function handleSearch(event) {
    event.preventDefault();

    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();

    if (query) {
        window.location.href = `../search.html?q=${encodeURIComponent(query)}`;
    }
}
