// Data processing and API layer for Play Store website

// Global data storage
let appData = null;
let categoriesData = null;

// Translation mappings for Chinese to English
const translations = {
    // Common Chinese terms that might appear in data
    '商务': 'Business',
    '天气': 'Weather',
    '工具': 'Utilities',
    '旅行': 'Travel',
    '体育': 'Sports',
    '社交': 'Social Networking',
    '游戏': 'Games',
    '娱乐': 'Entertainment',
    '教育': 'Education',
    '健康': 'Health',
    '新闻': 'News',
    '购物': 'Shopping',
    '摄影': 'Photography',
    '音乐': 'Music',
    '视频': 'Video',
    '阅读': 'Reading',
    '财务': 'Finance',
    '医疗': 'Medical',
    '导航': 'Navigation',
    '效率': 'Productivity'
};

// Initialize data loading
async function initializeData() {
    try {
        showLoading();
        await loadAppData();
        hideLoading();
        return true;
    } catch (error) {
        hideLoading();
        handleError(error, 'loading data');
        return false;
    }
}

// Load main app data from typeList.json
async function loadAppData() {
    try {
        const response = await fetch(getDataPath('data/typeList.json'));
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Process and translate data
        appData = {
            metadata: data.metadata,
            apps: data.rankInfo.map(app => translateAppData(app))
        };
        
        // Extract categories from metadata
        categoriesData = Object.entries(data.metadata.genres).map(([id, name]) => ({
            id,
            name: translateText(name),
            count: appData.apps.filter(app => app.type === translateText(name)).length
        }));
        
        return appData;
    } catch (error) {
        console.error('Error loading app data:', error);
        throw error;
    }
}

// Load individual app details
async function loadAppDetails(appId) {
    try {
        const response = await fetch(getDataPath(`data/appInfo/${appId}.json`));
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return translateAppDetailsData(data);
    } catch (error) {
        console.error(`Error loading app details for ${appId}:`, error);
        throw error;
    }
}

// Get correct data path based on current location
function getDataPath(path) {
    const currentPath = window.location.pathname;
    let basePath = '';
    
    if (currentPath.includes('/types/')) {
        basePath = '../';
    } else if (currentPath.includes('/details/')) {
        basePath = '../';
    }
    
    return basePath + path;
}

// Translate app data
function translateAppData(app) {
    return {
        ...app,
        appName: translateText(app.appName),
        subtitle: translateText(app.subtitle),
        publisher: translateText(app.publisher),
        type: translateText(app.type)
    };
}

// Translate app details data
function translateAppDetailsData(app) {
    return {
        ...app,
        trackName: translateText(app.trackName || app.appName),
        artistName: translateText(app.artistName || app.publisher),
        description: translateText(app.description),
        genres: app.genres ? app.genres.map(genre => translateText(genre)) : [],
        primaryGenreName: translateText(app.primaryGenreName)
    };
}

// Translate text using translation mappings
function translateText(text) {
    if (!text || typeof text !== 'string') return text;
    
    // Check for direct translation
    if (translations[text]) {
        return translations[text];
    }
    
    // Check for partial matches
    let translatedText = text;
    Object.entries(translations).forEach(([chinese, english]) => {
        translatedText = translatedText.replace(new RegExp(chinese, 'g'), english);
    });
    
    return translatedText;
}

// Get apps by category
function getAppsByCategory(category, limit = null) {
    if (!appData) return [];
    
    const filteredApps = appData.apps.filter(app => app.type === category);
    return limit ? filteredApps.slice(0, limit) : filteredApps;
}

// Search apps by name
function searchApps(query, filters = {}) {
    if (!appData || !query) return [];
    
    const searchTerm = query.toLowerCase();
    let results = appData.apps.filter(app => 
        app.appName.toLowerCase().includes(searchTerm)
    );
    
    // Apply category filter
    if (filters.category) {
        results = results.filter(app => app.type === filters.category);
    }
    
    // Apply rating filter
    if (filters.minRating) {
        results = results.filter(app => app.rating >= parseFloat(filters.minRating));
    }
    
    // Apply sorting
    if (filters.sort) {
        results = sortApps(results, filters.sort);
    }
    
    return results;
}

// Sort apps by different criteria
function sortApps(apps, sortBy) {
    const sortedApps = [...apps];
    
    switch (sortBy) {
        case 'rating':
            return sortedApps.sort((a, b) => b.rating - a.rating);
        case 'name':
            return sortedApps.sort((a, b) => a.appName.localeCompare(b.appName));
        case 'recent':
            return sortedApps.sort((a, b) => new Date(b.lastReleaseTime) - new Date(a.lastReleaseTime));
        case 'popular':
            return sortedApps.sort((a, b) => b.num - a.num);
        case 'relevance':
        default:
            return sortedApps;
    }
}

// Get featured apps for homepage
function getFeaturedApps() {
    if (!appData) return {};
    
    const categories = ['Business', 'Weather', 'Utilities', 'Travel', 'Sports'];
    const featured = {};
    
    categories.forEach(category => {
        const categoryApps = getAppsByCategory(category);
        // Get top rated apps from each category
        const topApps = sortApps(categoryApps, 'rating').slice(0, 10);
        featured[category.toLowerCase()] = topApps;
    });
    
    return featured;
}

// Get app statistics
function getAppStats() {
    if (!appData) return null;
    
    return {
        totalApps: appData.apps.length,
        categories: categoriesData,
        averageRating: appData.apps.reduce((sum, app) => sum + app.rating, 0) / appData.apps.length,
        totalReviews: appData.apps.reduce((sum, app) => sum + app.num, 0)
    };
}

// Get related apps (same category, excluding current app)
function getRelatedApps(currentAppId, category, limit = 6) {
    if (!appData) return [];
    
    return appData.apps
        .filter(app => app.appId !== currentAppId && app.type === category)
        .sort((a, b) => b.rating - a.rating)
        .slice(0, limit);
}

// Get top apps across all categories
function getTopApps(limit = 10) {
    if (!appData) return [];
    
    return sortApps(appData.apps, 'rating').slice(0, limit);
}

// Get recently updated apps
function getRecentlyUpdatedApps(limit = 10) {
    if (!appData) return [];
    
    return sortApps(appData.apps, 'recent').slice(0, limit);
}

// Get apps by multiple categories
function getAppsByCategories(categories, limit = null) {
    if (!appData) return [];
    
    const filteredApps = appData.apps.filter(app => categories.includes(app.type));
    return limit ? filteredApps.slice(0, limit) : filteredApps;
}

// Validate app data
function validateAppData(app) {
    const required = ['appId', 'appName', 'type'];
    return required.every(field => app && app[field] !== undefined && app[field] !== null);
}

// Get app by ID
function getAppById(appId) {
    if (!appData) return null;
    
    return appData.apps.find(app => app.appId == appId);
}

// Get categories list
function getCategories() {
    return categoriesData || [];
}

// Check if data is loaded
function isDataLoaded() {
    return appData !== null && categoriesData !== null;
}

// Refresh data
async function refreshData() {
    appData = null;
    categoriesData = null;
    return await initializeData();
}

// Export functions for use in other files
window.DataAPI = {
    initializeData,
    loadAppDetails,
    getAppsByCategory,
    searchApps,
    sortApps,
    getFeaturedApps,
    getAppStats,
    getRelatedApps,
    getTopApps,
    getRecentlyUpdatedApps,
    getAppsByCategories,
    getAppById,
    getCategories,
    isDataLoaded,
    refreshData,
    validateAppData
};

// Auto-initialize data when script loads
document.addEventListener('DOMContentLoaded', function() {
    // Don't auto-initialize, let each page control when to load data
    // This prevents unnecessary loading on pages that might not need it immediately
});
