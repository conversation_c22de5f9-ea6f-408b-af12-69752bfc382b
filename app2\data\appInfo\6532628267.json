{"artistViewUrl": "https://apps.apple.com/us/developer/titano-digital-teknoloji-limited-%C5%9Firketi/id**********?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/8c/b3/ee/8cb3ee50-238c-cba0-0ccb-c1598a50ac01/AppIcon-0-0-1x_U007emarketing-0-8-0-85-220.png/60x60bb.jpg", "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/8c/b3/ee/8cb3ee50-238c-cba0-0ccb-c1598a50ac01/AppIcon-0-0-1x_U007emarketing-0-8-0-85-220.png/512x512bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/8c/b3/ee/8cb3ee50-238c-cba0-0ccb-c1598a50ac01/AppIcon-0-0-1x_U007emarketing-0-8-0-85-220.png/100x100bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/83/27/a6/8327a69b-9a32-fd88-eb41-3b30d54c18b9/fba3206f-dcd5-468c-91d3-fb3e5dd78a89_1.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/89/41/ae/8941ae22-b7dd-1c4f-54d5-40d0751021ca/c982b12e-b323-4642-9634-280c1e5feff7_2.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/25/09/5b/25095b09-e6f0-47d3-96c8-85e855661202/cbbcac67-ccc0-4e04-bb19-24be66e06869_3.png/392x696bb.png"], "features": [], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": ["Infrequent/Mild Mature/Suggestive Themes", "Infrequent/Mild Medical/Treatment Information"], "isGameCenterEnabled": false, "kind": "software", "ipadScreenshotUrls": [], "appletvScreenshotUrls": [], "averageUserRatingForCurrentVersion": 4.69991, "averageUserRating": 4.69991, "minimumOsVersion": "17.0", "trackCensoredName": "<PERSON><PERSON> - <PERSON>", "languageCodesISO2A": ["EN"], "fileSizeBytes": "36136960", "formattedPrice": "Free", "contentAdvisoryRating": "12+", "userRatingCountForCurrentVersion": 6668, "trackContentRating": "12+", "trackViewUrl": "https://apps.apple.com/us/app/beam-tanning-get-tan-fast/id**********?uo=4", "artistId": **********, "artistName": "Titano Digital Teknoloji Limited Şirketi", "genres": ["Weather", "Lifestyle"], "price": 0, "trackId": **********, "trackName": "<PERSON><PERSON> - <PERSON>", "bundleId": "com.deepansh.Tan", "isVppDeviceBasedLicensingEnabled": true, "releaseDate": "2024-07-13T07:00:00Z", "currentVersionReleaseDate": "2025-06-03T18:17:59Z", "sellerName": "TITANO DIGITAL TEKNOLOJI LIMITED SIRKETI", "genreIds": ["6001", "6012"], "releaseNotes": "We swept away the bugs and gave stability a glow-up.\nNow everything runs smoother—so you can focus on that flawless, golden tan.", "version": "1.13", "wrapperType": "software", "currency": "USD", "description": "The fastest way to tan your body using the Beam tanning app. The Beam tanning app will give you the most customized routine based on your current location uv index to get the best possible tan as fast as possible. It will also suggest you ways to prevent sunburn and minimize the risk of sun. Remember to always use a sunscreen when going out in the sun. The customized routine and sun exposure prevention suggestions are only available with an additional paid in-app subscription. UV-Index based on current location is free to use for all users.\n\nFeatures (only available with an additional paid subscription plan):\n- Set your desired tone\n- Create a custom routine\n- Complete the routine\n- Achieve your desired shade\n\nSubscriptions:\nPayment will be charged to iTunes Account at confirmation of purchase. Subscription automatically renews unless auto-renew is turned off at least 24-hours before the end of the current period. Account will be charged for renewal within 24-hours prior to the end of the current period, and identify the cost of the renewal. \n\nSubscriptions can be managed by the user and auto-renewal may be turned off by going to the user's Account Settings after purchase. The user will still receive access to all Pro features until the end of current period. Any unused portion of a free trial period, if offered, will be forfeited when the user purchases a subscription to that publication, where applicable.\n\nPrivacy Policy: https://www.termsfeed.com/live/************************************\nTerms of Use: https://www.termsfeed.com/live/9adf5635-be9b-4234-b8b2-b12270566772\n\nDisclaimer:\nThe analysis and advice provided by this app are for informational purposes. The app does not provide medical diagnoses, treatment advice, or guidance. Always seek the advice of qualified professionals with any questions you may have regarding risks involving exposure to the sun.\n\nThe app uses Apple's Weather service to get weather information like uv index etc. Users can get more information about Apple's WeatherKit by visiting this link: https://weatherkit.apple.com/legal-attribution.html", "primaryGenreName": "Weather", "primaryGenreId": 6001, "userRatingCount": 6668}