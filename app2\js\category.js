// Category page specific JavaScript

// Category state
let currentCategory = '';
let currentSort = 'rating';
let currentView = 'grid';
let categoryApps = [];
let displayedApps = 0;
const appsPerPage = 20;

// Initialize category page
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Initialize data
        const dataLoaded = await DataAPI.initializeData();
        if (dataLoaded) {
            initializeCategoryPage();
        } else {
            showNoApps('Failed to load app data. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error initializing category page:', error);
        showNoApps('Something went wrong. Please refresh the page.');
    }
});

// Initialize category page functionality
function initializeCategoryPage() {
    // Get category from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    currentCategory = urlParams.get('category') || 'Business';
    
    // Load category data
    loadCategoryData();
    
    // Set active navigation link
    setActiveCategoryNav();
}

// Load category data and display apps
function loadCategoryData() {
    try {
        // Get apps for this category
        categoryApps = DataAPI.getAppsByCategory(currentCategory);
        
        // Apply current sorting
        categoryApps = DataAPI.sortApps(categoryApps, currentSort);
        
        // Update page content
        updateCategoryHeader();
        updateAppsCount();
        displayCategoryApps();
        toggleLoadMoreButton();
        
    } catch (error) {
        console.error('Error loading category data:', error);
        showNoApps('Error loading apps for this category.');
    }
}

// Update category header information
function updateCategoryHeader() {
    const titleElement = document.getElementById('categoryTitle');
    const subtitleElement = document.getElementById('categorySubtitle');
    const breadcrumbElement = document.getElementById('categoryBreadcrumb');
    
    if (titleElement) {
        titleElement.textContent = `${currentCategory} Apps`;
    }
    
    if (subtitleElement) {
        subtitleElement.textContent = `Discover amazing ${currentCategory.toLowerCase()} apps to enhance your experience`;
    }
    
    if (breadcrumbElement) {
        breadcrumbElement.textContent = currentCategory;
    }
    
    // Update page title
    document.title = `${currentCategory} Apps - Play Store`;
}

// Update apps count display
function updateAppsCount() {
    const countElement = document.getElementById('appsCount');
    const count = categoryApps.length;
    
    if (countElement) {
        if (count === 0) {
            countElement.textContent = 'No apps found';
        } else if (count === 1) {
            countElement.textContent = '1 app';
        } else {
            countElement.textContent = `${count} apps`;
        }
    }
}

// Display category apps
function displayCategoryApps() {
    const container = document.getElementById('appsContainer');
    const noAppsElement = document.getElementById('noApps');
    
    if (!container) return;
    
    if (categoryApps.length === 0) {
        container.innerHTML = '';
        if (noAppsElement) {
            noAppsElement.style.display = 'block';
        }
        return;
    }
    
    if (noAppsElement) {
        noAppsElement.style.display = 'none';
    }
    
    // Get apps to display
    const appsToShow = categoryApps.slice(0, displayedApps + appsPerPage);
    displayedApps = appsToShow.length;
    
    // Generate HTML for apps
    container.innerHTML = appsToShow.map(app => createAppCardHTML(app)).join('');
}

// Create HTML for a single app card
function createAppCardHTML(app) {
    return `
        <div class="app-card" onclick="navigateToApp(${app.appId})">
            <div class="app-card-header">
                <img src="${app.icon}" alt="${app.appName}" class="app-card-icon"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
                <div class="app-card-info">
                    <a href="../details/detail.html?id=${app.appId}" class="app-card-title">${app.appName}</a>
                    <div class="app-card-developer">${app.publisher}</div>
                </div>
            </div>
            <div class="app-card-rating">
                <div class="rating-stars">${generateStars(app.rating, 16)}</div>
                <span class="app-card-rating-value">${app.rating.toFixed(1)} (${formatNumber(app.num)})</span>
            </div>
            <div class="app-card-description">
                ${app.subtitle || 'Discover this amazing app and enhance your mobile experience with its powerful features and intuitive design.'}
            </div>
            <div class="app-card-footer">
                <span class="app-card-category">${app.type}</span>
                <span class="app-card-updated">Updated ${formatDate(app.lastReleaseTime)}</span>
            </div>
        </div>
    `;
}

// Change view between grid and list
function changeView(view) {
    currentView = view;
    
    const container = document.getElementById('appsContainer');
    const viewButtons = document.querySelectorAll('.view-btn');
    
    // Update button states
    viewButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.view === view) {
            btn.classList.add('active');
        }
    });
    
    // Update container class
    if (container) {
        container.className = view === 'list' ? 'apps-grid list-view' : 'apps-grid';
    }
}

// Apply sorting
function applySorting() {
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        currentSort = sortSelect.value;
        
        // Re-sort and display apps
        categoryApps = DataAPI.sortApps(categoryApps, currentSort);
        displayedApps = 0;
        displayCategoryApps();
        toggleLoadMoreButton();
    }
}

// Load more apps
function loadMoreApps() {
    if (displayedApps < categoryApps.length) {
        displayCategoryApps();
        toggleLoadMoreButton();
    }
}

// Toggle load more button visibility
function toggleLoadMoreButton() {
    const container = document.getElementById('loadMoreContainer');
    const button = document.getElementById('loadMoreBtn');
    
    if (!container || !button) return;
    
    if (displayedApps < categoryApps.length) {
        container.style.display = 'block';
        button.disabled = false;
        button.textContent = `Load More Apps (${categoryApps.length - displayedApps} remaining)`;
    } else {
        container.style.display = 'none';
    }
}

// Show no apps message
function showNoApps(message = 'No apps found in this category') {
    const container = document.getElementById('appsContainer');
    const noAppsElement = document.getElementById('noApps');
    
    if (container) {
        container.innerHTML = '';
    }
    
    if (noAppsElement) {
        noAppsElement.style.display = 'block';
        
        const textElement = noAppsElement.querySelector('.no-apps-text');
        if (textElement) {
            textElement.textContent = message;
        }
    }
}

// Set active navigation link
function setActiveCategoryNav() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        
        const linkHref = link.getAttribute('href');
        if (linkHref && linkHref.includes(`category=${currentCategory}`)) {
            link.classList.add('active');
        }
    });
}

// Navigate to app details
function navigateToApp(appId) {
    if (appId) {
        window.location.href = `../details/detail.html?id=${appId}`;
    }
}

// Handle search form submission
function handleSearch(event) {
    event.preventDefault();
    
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();
    
    if (query) {
        window.location.href = `../search.html?q=${encodeURIComponent(query)}`;
    }
}

// Initialize scroll animations for app cards
function initializeScrollAnimations() {
    const cards = document.querySelectorAll('.app-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 50);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
        observer.observe(card);
    });
}

// Initialize animations after apps are loaded
function initializeAnimations() {
    setTimeout(() => {
        initializeScrollAnimations();
    }, 100);
}

// Re-initialize animations when apps are updated
function updateAnimations() {
    setTimeout(() => {
        initializeScrollAnimations();
    }, 50);
}

// Override the displayCategoryApps function to include animations
const originalDisplayCategoryApps = displayCategoryApps;
displayCategoryApps = function() {
    originalDisplayCategoryApps();
    updateAnimations();
};

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAnimations, 500);
});
