<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Details - Play Store</title>
    <meta name="description" content="Detailed information about the app">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/details.css">
    <link rel="icon" type="image/svg+xml" href="../images/logo.svg">
</head>

<body>
    <!-- Header with navigation and search -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo">
                    <a href="../index.html">
                        <img src="../images/logo.svg" alt="Play Store Logo" class="logo-img">
                        <span class="logo-text">Play Store</span>
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="../index.html" class="nav-link">Apps</a></li>
                        <li><a href="../types/type.html?category=Business" class="nav-link">Business</a></li>
                        <li><a href="../types/type.html?category=Weather" class="nav-link">Weather</a></li>
                        <li><a href="../types/type.html?category=Utilities" class="nav-link">Utilities</a></li>
                        <li><a href="../types/type.html?category=Travel" class="nav-link">Travel</a></li>
                        <li><a href="../types/type.html?category=Sports" class="nav-link">Sports</a></li>
                        <li><a href="../types/type.html?category=Social Networking" class="nav-link">Social</a></li>
                    </ul>
                </nav>

                <!-- Search -->
                <div class="search-container">
                    <form class="search-form" onsubmit="handleSearch(event)">
                        <div class="search-input-wrapper">
                            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            <input type="text" class="search-input" placeholder="Search for apps" id="searchInput">
                        </div>
                        <button type="submit" class="search-btn">Search</button>
                    </form>
                </div>

                <!-- Mobile menu toggle -->
                <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>

                <!-- Mobile search toggle -->
                <button class="mobile-search-toggle" onclick="toggleMobileSearch()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile menu overlay -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay">
        <div class="mobile-menu-header">
            <h2 class="mobile-menu-title">Menu</h2>
            <button class="mobile-menu-close" onclick="closeMobileMenu()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <div class="mobile-search-section">
            <form class="mobile-search-form" onsubmit="handleMobileSearch(event)">
                <input type="text" class="mobile-search-input" placeholder="Search for apps" id="mobileSearchInput">
                <button type="submit" class="mobile-search-btn">Search</button>
            </form>
        </div>
        <div class="mobile-menu-content">
            <ul class="mobile-categories-list">
                <li class="mobile-category-item">
                    <a href="../types/type.html?category=Business" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                        Business Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="../types/type.html?category=Weather" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>
                        </svg>
                        Weather Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="../types/type.html?category=Utilities" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z">
                            </path>
                        </svg>
                        Utilities Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="../types/type.html?category=Travel" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z">
                            </path>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                            <line x1="12" y1="22.08" x2="12" y2="12"></line>
                        </svg>
                        Travel Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="../types/type.html?category=Sports" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path>
                            <path d="M2 12h20"></path>
                        </svg>
                        Sports Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="../types/type.html?category=Social Networking" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        Social Apps
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Categories section -->
    <section class="categories-section">
        <div class="categories-container">
            <ul class="categories-list">
                <li class="category-item">
                    <a href="types/type.html?category=Business" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                        Business
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Weather" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>
                        </svg>
                        Weather
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Utilities" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z">
                            </path>
                        </svg>
                        Utilities
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Travel" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z">
                            </path>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                            <line x1="12" y1="22.08" x2="12" y2="12"></line>
                        </svg>
                        Travel
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Sports" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path>
                            <path d="M2 12h20"></path>
                        </svg>
                        Sports
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Social Networking" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        Social
                    </a>
                </li>
            </ul>
        </div>
    </section>
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <!-- App header with background -->
            <div class="app-header" id="appHeader">
                <div class="app-header-overlay"></div>
                <div class="app-header-content">
                    <nav class="breadcrumb">
                        <a href="../index.html" class="breadcrumb-link">Apps</a>
                        <span class="breadcrumb-separator">/</span>
                        <a href="#" class="breadcrumb-link" id="categoryBreadcrumb">Category</a>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current" id="appBreadcrumb">App</span>
                    </nav>

                    <div class="app-info">
                        <div class="app-icon-container">
                            <img src="" alt="App Icon" class="app-icon" id="appIcon">
                        </div>

                        <div class="app-details">
                            <div class="app-category-badge" id="appCategoryBadge">Category</div>
                            <h1 class="app-title" id="appTitle">App Name</h1>
                            <p class="app-developer" id="appDeveloper">Developer</p>
                            <div class="app-rating">
                                <div class="rating-stars" id="ratingStars">
                                    <!-- Stars will be generated dynamically -->
                                </div>
                                <span class="rating-value" id="ratingValue">0.0</span>
                                <span class="rating-count" id="ratingCount">(0 reviews)</span>
                            </div>
                            <div class="app-category">
                                <span class="category-label">Category:</span>
                                <span class="category-value" id="appCategory">Category</span>
                            </div>
                        </div>

                        <div class="app-actions">
                            <button class="install-btn" onclick="installApp()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7,10 12,15 17,10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                                Install
                            </button>
                            <button class="wishlist-btn" onclick="toggleWishlist()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2">
                                    <path
                                        d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z">
                                    </path>
                                </svg>
                            </button>
                            <button class="share-btn" onclick="shareApp()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2">
                                    <circle cx="18" cy="5" r="3"></circle>
                                    <circle cx="6" cy="12" r="3"></circle>
                                    <circle cx="18" cy="19" r="3"></circle>
                                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- App content -->
            <div class="app-content">
                <div class="content-layout">
                    <!-- Main content -->
                    <div class="main-content">
                        <!-- Screenshots section -->
                        <section class="screenshots-section" id="screenshotsSection">
                            <h2 class="section-title">Screenshots</h2>
                            <div class="screenshots-container">
                                <div class="screenshots-scroll" id="screenshotsScroll">
                                    <!-- Screenshots will be loaded dynamically -->
                                </div>
                            </div>
                        </section>

                        <!-- Description section -->
                        <section class="description-section">
                            <h2 class="section-title">About this app</h2>
                            <div class="description-content">
                                <div class="description-text" id="appDescription">
                                    <!-- Description will be loaded dynamically -->
                                </div>
                                <button class="read-more-btn" id="readMoreBtn" onclick="toggleDescription()"
                                    style="display: none;">
                                    Read more
                                </button>
                            </div>
                        </section>

                        <!-- App info section -->
                        <section class="app-info-section">
                            <h2 class="section-title">App info</h2>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">Version</span>
                                    <span class="info-value" id="appVersion">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Size</span>
                                    <span class="info-value" id="appSize">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Updated</span>
                                    <span class="info-value" id="appUpdated">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Requires</span>
                                    <span class="info-value" id="appRequires">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Content Rating</span>
                                    <span class="info-value" id="appContentRating">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Developer</span>
                                    <span class="info-value" id="appDeveloperInfo">-</span>
                                </div>
                            </div>
                        </section>

                        <!-- Ratings section -->
                        <section class="ratings-section">
                            <h2 class="section-title">Ratings and reviews</h2>
                            <div class="ratings-overview">
                                <div class="rating-summary">
                                    <div class="rating-score" id="overallRating">0.0</div>
                                    <div class="rating-stars-large" id="overallStars">
                                        <!-- Stars will be generated dynamically -->
                                    </div>
                                    <div class="rating-total" id="totalReviews">0 reviews</div>
                                </div>
                                <div class="rating-breakdown">
                                    <div class="rating-bar">
                                        <span class="rating-label">5</span>
                                        <div class="bar-container">
                                            <div class="bar-fill" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="rating-bar">
                                        <span class="rating-label">4</span>
                                        <div class="bar-container">
                                            <div class="bar-fill" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="rating-bar">
                                        <span class="rating-label">3</span>
                                        <div class="bar-container">
                                            <div class="bar-fill" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="rating-bar">
                                        <span class="rating-label">2</span>
                                        <div class="bar-container">
                                            <div class="bar-fill" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="rating-bar">
                                        <span class="rating-label">1</span>
                                        <div class="bar-container">
                                            <div class="bar-fill" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>

                    <!-- Sidebar with related apps -->
                    <div class="sidebar">
                        <section class="related-apps-section">
                            <h3 class="sidebar-title">More like this</h3>
                            <div class="related-apps-list" id="relatedAppsList">
                                <!-- Related apps will be loaded dynamically -->
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-brand">
                    <div class="footer-logo">
                        <img src="../images/logo.svg" alt="Play Store Logo" class="footer-logo-img">
                        <span class="footer-logo-text">Play Store</span>
                    </div>
                    <p class="footer-description">Your ultimate destination for discovering and downloading amazing
                        apps. Find the perfect tools and entertainment for your digital life.</p>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Legal</h3>
                    <ul class="footer-links">
                        <li><a href="../Privacy.html" class="footer-link">Privacy Policy</a></li>
                        <li><a href="../Terms.html" class="footer-link">Terms of Service</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Hot Links</h3>
                    <ul class="footer-links">
                        <li><a href="../types/type.html?category=Business" class="footer-link">Business</a></li>
                        <li><a href="../types/type.html?category=Weather" class="footer-link">Weather</a></li>
                        <li><a href="../types/type.html?category=Utilities" class="footer-link">Utilities</a></li>
                        <li><a href="../types/type.html?category=Travel" class="footer-link">Travel</a></li>
                        <li><a href="../types/type.html?category=Sports" class="footer-link">Sports</a></li>
                        <li><a href="../types/type.html?category=Social Networking" class="footer-link">Social</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Play Store. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to top button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m18 15-6-6-6 6" />
        </svg>
    </button>

    <!-- Loading indicator -->
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
        <p>Loading app details...</p>
    </div>

    <!-- Scripts -->
    <script src="../js/common.js"></script>
    <script src="../js/data.js"></script>
    <script src="../js/details.js"></script>
</body>

</html>