// Common JavaScript functions for Play Store website

// Global variables
let isMobileMenuOpen = false;

// Initialize common functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeBackToTop();
    initializeNavigation();
    initializeMobileMenu();
    
    // Set active navigation link based on current page
    setActiveNavLink();
});

// Back to top functionality
function initializeBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    
    if (backToTopBtn) {
        // Show/hide back to top button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
    }
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Navigation functionality
function initializeNavigation() {
    // Add click handlers to navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            // Add active class to clicked link
            this.classList.add('active');
        });
    });
}

function setActiveNavLink() {
    const currentPath = window.location.pathname;
    const currentParams = new URLSearchParams(window.location.search);
    const category = currentParams.get('category');
    
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        
        const linkHref = link.getAttribute('href');
        
        // Check for exact match or category match
        if (linkHref === 'index.html' && (currentPath.endsWith('index.html') || currentPath.endsWith('/'))) {
            link.classList.add('active');
        } else if (linkHref.includes('category=') && category) {
            const linkCategory = new URL(linkHref, window.location.origin).searchParams.get('category');
            if (linkCategory === category) {
                link.classList.add('active');
            }
        }
    });
}

// Mobile menu functionality
function initializeMobileMenu() {
    // Mobile menu is now handled by HTML structure and CSS
    // No additional initialization needed
}

function toggleMobileMenu() {
    const overlay = document.getElementById('mobileMenuOverlay');
    const toggle = document.querySelector('.mobile-menu-toggle');

    if (!overlay || !toggle) return;

    if (isMobileMenuOpen) {
        closeMobileMenu();
    } else {
        openMobileMenu();
    }
}

function openMobileMenu() {
    const overlay = document.getElementById('mobileMenuOverlay');
    const toggle = document.querySelector('.mobile-menu-toggle');

    if (!overlay || !toggle) return;

    isMobileMenuOpen = true;
    overlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animate hamburger to X
    const spans = toggle.querySelectorAll('span');
    if (spans.length === 3) {
        spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
        spans[1].style.opacity = '0';
        spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
    }
}

function closeMobileMenu() {
    const overlay = document.getElementById('mobileMenuOverlay');
    const toggle = document.querySelector('.mobile-menu-toggle');

    if (!overlay || !toggle) return;

    isMobileMenuOpen = false;
    overlay.classList.remove('active');
    document.body.style.overflow = '';

    // Reset hamburger
    const spans = toggle.querySelectorAll('span');
    if (spans.length === 3) {
        spans[0].style.transform = '';
        spans[1].style.opacity = '';
        spans[2].style.transform = '';
    }
}

// Search functionality
function handleSearch(event) {
    event.preventDefault();

    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();

    if (query) {
        // Navigate to search page with query parameter
        const searchUrl = getSearchUrl(query);
        window.location.href = searchUrl;
    }
}

// Mobile search functionality
function toggleMobileSearch() {
    // Open mobile menu which now contains search
    toggleMobileMenu();

    // Focus on search input after menu opens
    setTimeout(() => {
        const searchInput = document.getElementById('mobileSearchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }, 300);
}

function closeMobileSearch() {
    closeMobileMenu();
}

function handleMobileSearch(event) {
    event.preventDefault();

    const searchInput = document.getElementById('mobileSearchInput');
    const query = searchInput.value.trim();

    if (query) {
        closeMobileMenu();
        const searchUrl = getSearchUrl(query);
        window.location.href = searchUrl;
    }
}

function getSearchUrl(query) {
    const currentPath = window.location.pathname;
    let basePath = '';
    
    if (currentPath.includes('/types/')) {
        basePath = '../';
    } else if (currentPath.includes('/details/')) {
        basePath = '../';
    }
    
    return `${basePath}search.html?q=${encodeURIComponent(query)}`;
}

// Loading indicator functions
function showLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.classList.add('visible');
    }
}

function hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.classList.remove('visible');
    }
}

// Utility functions
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
        return 'Yesterday';
    } else if (diffDays < 7) {
        return `${diffDays} days ago`;
    } else if (diffDays < 30) {
        const weeks = Math.floor(diffDays / 7);
        return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
    } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
        const years = Math.floor(diffDays / 365);
        return `${years} year${years > 1 ? 's' : ''} ago`;
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Rating stars generation
function generateStars(rating, size = 16) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let starsHTML = '';
    
    // Full stars
    for (let i = 0; i < fullStars; i++) {
        starsHTML += `<svg class="star filled" width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>`;
    }
    
    // Half star
    if (hasHalfStar) {
        starsHTML += `<svg class="star half-filled" width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor">
            <defs>
                <linearGradient id="half-star">
                    <stop offset="50%" stop-color="#fbbc04"/>
                    <stop offset="50%" stop-color="#dadce0"/>
                </linearGradient>
            </defs>
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>`;
    }
    
    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += `<svg class="star" width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>`;
    }
    
    return starsHTML;
}

// Error handling
function handleError(error, context = '') {
    console.error(`Error ${context}:`, error);
    
    // Show user-friendly error message
    const errorMessage = document.createElement('div');
    errorMessage.className = 'error-message';
    errorMessage.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ea4335;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(234, 67, 53, 0.3);
        animation: slideIn 0.3s ease;
    `;
    errorMessage.textContent = 'Something went wrong. Please try again.';
    
    document.body.appendChild(errorMessage);
    
    // Remove error message after 5 seconds
    setTimeout(() => {
        if (errorMessage.parentNode) {
            errorMessage.remove();
        }
    }, 5000);
}

// Add CSS for error message animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
