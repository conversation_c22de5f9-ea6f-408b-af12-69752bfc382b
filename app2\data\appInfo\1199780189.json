{"features": [], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "isGameCenterEnabled": false, "kind": "software", "artistViewUrl": "https://apps.apple.com/us/developer/neutron-holdings-inc/id1199780188?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/a8/66/17/a86617c7-9219-7f1c-4e45-a92c102315d7/AppIcon-0-0-1x_U007emarketing-0-6-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/a8/66/17/a86617c7-9219-7f1c-4e45-a92c102315d7/AppIcon-0-0-1x_U007emarketing-0-6-0-85-220.png/100x100bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/16/33/ac/1633ac34-6e04-aafe-c0c9-6847c8aadd09/c9a5cb82-8292-4191-8689-28fefc4c3572_en_S_1.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/d5/77/c4/d577c452-9c77-cc6b-7abe-a4d869bc9245/5b6abd3b-62a6-4ef1-99c6-2c6d8d650dbc_en_S_2.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/28/20/fc/2820fca2-dda5-57bd-cbc0-38bd84a4a1e8/b7c52f35-8a88-4335-8d02-63cf9ecbe379_en_S_3.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/c1/aa/e6/c1aae6e3-41d5-c78f-bb6a-c42e657599d2/f05dd871-b9f2-4ecf-acf6-cbe0997ebfa2_en_S_4.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/98/9f/a0/989fa00c-69ed-f267-b3f1-235b97025398/da7c0615-870a-4f7f-820f-e4ed538db6ce_en_S_5.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/65/f9/60/65f96052-a64e-51e1-f45c-74d43756aa64/b9b87fc1-8326-423f-b398-ecac289d504a_en_S_6.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/77/d6/85/77d6855c-45ab-a8b5-93ae-7559d5c97699/ecc0a44f-e30b-43ec-9524-d5a22ea09f81_en_S_7.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/db/aa/f7/dbaaf774-89b9-4e01-0f9e-43ddd5fbefac/4d484ba2-8a20-4f08-bbc6-a8562fe5b605_en_S_8.jpg/392x696bb.jpg"], "ipadScreenshotUrls": [], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/a8/66/17/a86617c7-9219-7f1c-4e45-a92c102315d7/AppIcon-0-0-1x_U007emarketing-0-6-0-85-220.png/512x512bb.jpg", "artistId": 1199780188, "artistName": "Neutron Holdings. Inc.", "genres": ["Travel", "Lifestyle"], "price": 0, "releaseDate": "2017-05-09T23:44:43Z", "bundleId": "com.limebike", "sellerName": "Neutron Holdings. Inc.", "isVppDeviceBasedLicensingEnabled": true, "genreIds": ["6003", "6012"], "trackId": **********, "trackName": "Lime - #<PERSON><PERSON><PERSON>", "currentVersionReleaseDate": "2025-07-01T18:34:03Z", "primaryGenreName": "Travel", "primaryGenreId": 6003, "releaseNotes": "Bug fixes and performance improvements", "version": "3.219.0", "wrapperType": "software", "currency": "USD", "description": "You have places to be and people to see. Get there easily and on time with an emissions-free Lime e-bike or e-scooter! \n\nSTART YOUR RIDE IN 3 STEPS\nStep 1\nDownload the app, create an account and accept our terms and conditions https://www.li.me/user-agreement\nPrivacy Notice \nhttps://www.li.me/legal/privacy-policy/ \n\nStep 2 \nFind a nearby Lime vehicle on the map (vehicle availability depends on your city and supply)\n\nStep 3\nUnlock your vehicle by scanning the QR code, entering the plate number, or by tapping a button on the app.\nRIDE RESPONSIBLY\nA safe community starts with riding responsibly. It’s important to remember the rules of the road before every ride. You should always:\n\n- Ride in bike lanes, never on sidewalks\n- Wear a helmet when you ride\n- Park clear of walkways, driveways and access ramps\n- Visit https://safety.li.me/ to learn more\n\n#RIDEGREEN\nLime is on a mission to build a future where transportation is shared, affordable and carbon-free.\n\n\nYou can read more about Lime’s products and services, including how we calculate our prices in our terms and conditions https://www.li.me/user-agreement.", "userRatingCountForCurrentVersion": 2142055, "averageUserRating": 4.93316, "trackCensoredName": "Lime - #<PERSON><PERSON><PERSON>", "trackViewUrl": "https://apps.apple.com/us/app/lime-ridegreen/id**********?uo=4", "contentAdvisoryRating": "4+", "averageUserRatingForCurrentVersion": 4.93316, "sellerUrl": "https://www.li.me/", "languageCodesISO2A": ["AR", "BG", "CA", "CS", "DA", "NL", "EN", "FI", "FR", "DE", "EL", "HE", "HU", "IT", "JA", "KO", "NB", "PL", "PT", "RO", "RU", "ZH", "ES", "SV", "TR"], "fileSizeBytes": "182659072", "formattedPrice": "Free", "trackContentRating": "4+", "minimumOsVersion": "14.0", "userRatingCount": 2142055}