<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category - Play Store</title>
    <meta name="description" content="Browse apps by category in our comprehensive app store">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/category.css">
    <link rel="icon" type="image/svg+xml" href="../images/logo.svg">
</head>

<body>
    <!-- Header with navigation and search -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo">
                    <a href="../index.html">
                        <img src="../images/logo.svg" alt="Play Store Logo" class="logo-img">
                        <span class="logo-text">Play Store</span>
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="../index.html" class="nav-link">Apps</a></li>
                        <li><a href="type.html?category=Business" class="nav-link">Business</a></li>
                        <li><a href="type.html?category=Weather" class="nav-link">Weather</a></li>
                        <li><a href="type.html?category=Utilities" class="nav-link">Utilities</a></li>
                        <li><a href="type.html?category=Travel" class="nav-link">Travel</a></li>
                        <li><a href="type.html?category=Sports" class="nav-link">Sports</a></li>
                        <li><a href="type.html?category=Social Networking" class="nav-link">Social</a></li>
                    </ul>
                </nav>

                <!-- Search -->
                <div class="search-container">
                    <form class="search-form" onsubmit="handleSearch(event)">
                        <div class="search-input-wrapper">
                            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            <input type="text" class="search-input" placeholder="Search for apps" id="searchInput">
                        </div>
                        <button type="submit" class="search-btn">Search</button>
                    </form>
                </div>

                <!-- Mobile menu toggle -->
                <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile menu overlay -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay">
        <div class="mobile-menu-header">
            <h2 class="mobile-menu-title">Menu</h2>
            <button class="mobile-menu-close" onclick="closeMobileMenu()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <div class="mobile-search-section">
            <form class="mobile-search-form" onsubmit="handleMobileSearch(event)">
                <input type="text" class="mobile-search-input" placeholder="Search for apps" id="mobileSearchInput">
                <button type="submit" class="mobile-search-btn">Search</button>
            </form>
        </div>
        <div class="mobile-menu-content">
            <ul class="mobile-categories-list">
                <li class="mobile-category-item">
                    <a href="type.html?category=Business" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                        Business Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="type.html?category=Weather" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>
                        </svg>
                        Weather Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="type.html?category=Utilities" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z">
                            </path>
                        </svg>
                        Utilities Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="type.html?category=Travel" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z">
                            </path>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                            <line x1="12" y1="22.08" x2="12" y2="12"></line>
                        </svg>
                        Travel Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="type.html?category=Sports" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path>
                            <path d="M2 12h20"></path>
                        </svg>
                        Sports Apps
                    </a>
                </li>
                <li class="mobile-category-item">
                    <a href="type.html?category=Social Networking" class="mobile-category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        Social Apps
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Categories section -->
    <section class="categories-section">
        <div class="categories-container">
            <ul class="categories-list">
                <li class="category-item">
                    <a href="types/type.html?category=Business" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                        Business
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Weather" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>
                        </svg>
                        Weather
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Utilities" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z">
                            </path>
                        </svg>
                        Utilities
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Travel" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path
                                d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z">
                            </path>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                            <line x1="12" y1="22.08" x2="12" y2="12"></line>
                        </svg>
                        Travel
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Sports" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path>
                            <path d="M2 12h20"></path>
                        </svg>
                        Sports
                    </a>
                </li>
                <li class="category-item">
                    <a href="types/type.html?category=Social Networking" class="category-link">
                        <svg class="category-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        Social
                    </a>
                </li>
            </ul>
        </div>
    </section>
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <!-- Category header -->
            <div class="category-header">
                <nav class="breadcrumb">
                    <a href="../index.html" class="breadcrumb-link">Apps</a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current" id="categoryBreadcrumb">Category</span>
                </nav>

                <h1 class="category-title" id="categoryTitle">Category Apps</h1>
                <p class="category-subtitle" id="categorySubtitle">Discover amazing apps in this category</p>

                <div class="category-stats">
                    <span class="apps-count" id="appsCount">0 apps</span>
                </div>
            </div>

            <!-- Category controls -->
            <div class="category-controls">
                <div class="view-controls">
                    <button class="view-btn active" data-view="grid" onclick="changeView('grid')">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        Grid
                    </button>
                    <button class="view-btn" data-view="list" onclick="changeView('list')">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2">
                            <line x1="8" y1="6" x2="21" y2="6"></line>
                            <line x1="8" y1="12" x2="21" y2="12"></line>
                            <line x1="8" y1="18" x2="21" y2="18"></line>
                            <line x1="3" y1="6" x2="3.01" y2="6"></line>
                            <line x1="3" y1="12" x2="3.01" y2="12"></line>
                            <line x1="3" y1="18" x2="3.01" y2="18"></line>
                        </svg>
                        List
                    </button>
                </div>

                <div class="sort-controls">
                    <label for="sortSelect" class="sort-label">Sort by:</label>
                    <select id="sortSelect" class="sort-select" onchange="applySorting()">
                        <option value="rating">Rating</option>
                        <option value="name">Name</option>
                        <option value="recent">Recently Updated</option>
                        <option value="popular">Most Popular</option>
                    </select>
                </div>
            </div>

            <!-- Apps container -->
            <div class="apps-container">
                <div class="apps-grid" id="appsContainer">
                    <!-- Apps will be loaded dynamically -->
                </div>
            </div>

            <!-- Load more button -->
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="load-more-btn" id="loadMoreBtn" onclick="loadMoreApps()">
                    Load More Apps
                </button>
            </div>

            <!-- No apps message -->
            <div class="no-apps" id="noApps" style="display: none;">
                <div class="no-apps-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                        <line x1="8" y1="21" x2="16" y2="21"></line>
                        <line x1="12" y1="17" x2="12" y2="21"></line>
                    </svg>
                </div>
                <h3 class="no-apps-title">No apps found</h3>
                <p class="no-apps-text">There are no apps available in this category at the moment.</p>
                <a href="../index.html" class="back-home-btn">Back to Home</a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-brand">
                    <div class="footer-logo">
                        <img src="../images/logo.svg" alt="Play Store Logo" class="footer-logo-img">
                        <span class="footer-logo-text">Play Store</span>
                    </div>
                    <p class="footer-description">Your ultimate destination for discovering and downloading amazing
                        apps. Find the perfect tools and entertainment for your digital life.</p>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Legal</h3>
                    <ul class="footer-links">
                        <li><a href="../Privacy.html" class="footer-link">Privacy Policy</a></li>
                        <li><a href="../Terms.html" class="footer-link">Terms of Service</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Hot Links</h3>
                    <ul class="footer-links">
                        <li><a href="type.html?category=Business" class="footer-link">Business</a></li>
                        <li><a href="type.html?category=Weather" class="footer-link">Weather</a></li>
                        <li><a href="type.html?category=Utilities" class="footer-link">Utilities</a></li>
                        <li><a href="type.html?category=Travel" class="footer-link">Travel</a></li>
                        <li><a href="type.html?category=Sports" class="footer-link">Sports</a></li>
                        <li><a href="type.html?category=Social Networking" class="footer-link">Social</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Play Store. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to top button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m18 15-6-6-6 6" />
        </svg>
    </button>

    <!-- Loading indicator -->
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
        <p>Loading apps...</p>
    </div>

    <!-- Scripts -->
    <script src="../js/common.js"></script>
    <script src="../js/data.js"></script>
    <script src="../js/category.js"></script>
</body>

</html>