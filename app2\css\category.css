/* Category page specific styles */

/* Category header */
.category-header {
    margin-bottom: 32px;
    padding: 24px 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-link:hover {
    color: white;
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-current {
    color: white;
}

.category-title {
    font-size: 2.2rem;
    font-weight: 500;
    color: white;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.category-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    margin-bottom: 16px;
}

.category-stats {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Category controls */
.category-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 16px 24px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    gap: 16px;
}

.view-controls {
    display: flex;
    gap: 8px;
}

.view-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid #dadce0;
    border-radius: 20px;
    color: #5f6368;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn:hover {
    background: #f8f9fa;
    border-color: #1a73e8;
    color: #1a73e8;
}

.view-btn.active {
    background: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-label {
    font-size: 0.9rem;
    color: #5f6368;
    font-weight: 500;
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    outline: none;
    transition: border-color 0.2s ease;
}

.sort-select:focus {
    border-color: #1a73e8;
}

/* Apps container */
.apps-container {
    margin-bottom: 32px;
    padding: 0 16px;
}

/* Grid view */
.apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.apps-grid.list-view {
    grid-template-columns: 1fr;
    gap: 12px;
}

/* App card */
.app-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.app-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.app-card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
}

.app-card-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    object-fit: cover;
    flex-shrink: 0;
}

.app-card-info {
    flex: 1;
    min-width: 0;
}

.app-card-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: #202124;
    margin-bottom: 4px;
    text-decoration: none;
    display: block;
}

.app-card-title:hover {
    color: #1a73e8;
}

.app-card-developer {
    color: #5f6368;
    font-size: 0.9rem;
}

.app-card-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.app-card-rating-value {
    color: #5f6368;
    font-size: 0.85rem;
}

.app-card-description {
    color: #5f6368;
    font-size: 0.9rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 12px;
}

.app-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}

.app-card-category {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.app-card-updated {
    color: #5f6368;
    font-size: 0.8rem;
}

/* List view specific styles */
.apps-grid.list-view .app-card {
    padding: 16px 20px;
}

.apps-grid.list-view .app-card-header {
    margin-bottom: 0;
}

.apps-grid.list-view .app-card-icon {
    width: 48px;
    height: 48px;
}

.apps-grid.list-view .app-card-description {
    -webkit-line-clamp: 1;
    margin-bottom: 0;
}

.apps-grid.list-view .app-card-rating {
    margin-bottom: 0;
}

/* Load more */
.load-more-container {
    text-align: center;
    margin-top: 32px;
}

.load-more-btn {
    background: white;
    color: #1a73e8;
    border: 1px solid #1a73e8;
    padding: 12px 24px;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.load-more-btn:hover {
    background: #e8f0fe;
}

.load-more-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* No apps */
.no-apps {
    text-align: center;
    padding: 48px 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.no-apps-icon {
    margin-bottom: 16px;
    color: #dadce0;
}

.no-apps-title {
    font-size: 1.2rem;
    font-weight: 400;
    color: #202124;
    margin-bottom: 8px;
}

.no-apps-text {
    color: #5f6368;
    margin-bottom: 20px;
}

.back-home-btn {
    background: #1a73e8;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
}

.back-home-btn:hover {
    background: #1557b0;
}

/* Responsive design */
@media (max-width: 768px) {
    .category-header {
        padding: 16px 0;
        margin-bottom: 24px;
    }
    
    .category-title {
        font-size: 1.8rem;
    }
    
    .category-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 16px;
    }
    
    .view-controls {
        justify-content: center;
    }
    
    .sort-controls {
        justify-content: space-between;
    }
    
    .apps-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 16px;
    }
    
    .apps-grid.list-view {
        gap: 8px;
    }
    
    .app-card {
        padding: 16px;
    }
    
    .apps-grid.list-view .app-card {
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .category-title {
        font-size: 1.6rem;
    }
    
    .apps-grid {
        grid-template-columns: 1fr;
    }
    
    .app-card-header {
        gap: 12px;
    }
    
    .app-card-icon {
        width: 48px;
        height: 48px;
    }
    
    .apps-grid.list-view .app-card-icon {
        width: 40px;
        height: 40px;
    }
}
