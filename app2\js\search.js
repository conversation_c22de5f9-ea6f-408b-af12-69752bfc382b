// Search page specific JavaScript

// Search state
let currentQuery = '';
let currentFilters = {
    category: '',
    sort: 'relevance',
    minRating: 0
};
let currentResults = [];
let displayedResults = 0;
const resultsPerPage = 20;

// Initialize search page
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Initialize data
        const dataLoaded = await DataAPI.initializeData();
        if (dataLoaded) {
            initializeSearchPage();
        } else {
            showNoResults('Failed to load app data. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error initializing search page:', error);
        showNoResults('Something went wrong. Please refresh the page.');
    }
});

// Initialize search page functionality
function initializeSearchPage() {
    // Get search query from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const query = urlParams.get('q');
    
    if (query) {
        currentQuery = query;
        document.getElementById('searchInput').value = query;
        performSearch();
    }
    
    // Update page title and header
    updateSearchHeader();
}

// Update search header based on query
function updateSearchHeader() {
    const titleElement = document.getElementById('searchTitle');
    const subtitleElement = document.getElementById('searchSubtitle');
    
    if (currentQuery) {
        titleElement.textContent = `Search results for "${currentQuery}"`;
        subtitleElement.textContent = `Found ${currentResults.length} apps matching your search`;
    } else {
        titleElement.textContent = 'Search Apps';
        subtitleElement.textContent = 'Enter a search term to find apps';
    }
}

// Perform search with current query and filters
function performSearch() {
    if (!currentQuery.trim()) {
        showNoResults('Please enter a search term');
        return;
    }
    
    try {
        // Search apps using DataAPI
        currentResults = DataAPI.searchApps(currentQuery, currentFilters);
        displayedResults = 0;
        
        // Update UI
        updateSearchHeader();
        updateResultsCount();
        displaySearchResults();
        
        // Show/hide load more button
        toggleLoadMoreButton();
        
    } catch (error) {
        console.error('Error performing search:', error);
        showNoResults('Error performing search. Please try again.');
    }
}

// Display search results
function displaySearchResults() {
    const container = document.getElementById('searchResults');
    const noResultsElement = document.getElementById('noResults');
    
    if (currentResults.length === 0) {
        container.innerHTML = '';
        noResultsElement.style.display = 'block';
        return;
    }
    
    noResultsElement.style.display = 'none';
    
    // Get results to display
    const resultsToShow = currentResults.slice(0, displayedResults + resultsPerPage);
    displayedResults = resultsToShow.length;
    
    // Generate HTML for results
    container.innerHTML = resultsToShow.map(app => createSearchResultHTML(app)).join('');
}

// Create HTML for a single search result
function createSearchResultHTML(app) {
    return `
        <div class="search-result-item" onclick="navigateToApp(${app.appId})">
            <img src="${app.icon}" alt="${app.appName}" class="search-result-icon"
                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
            <div class="search-result-content">
                <a href="details/detail.html?id=${app.appId}" class="search-result-title">${app.appName}</a>
                <div class="search-result-developer">${app.publisher}</div>
                <div class="search-result-description">
                    ${app.subtitle || 'Discover this amazing app and enhance your mobile experience with its powerful features and intuitive design.'}
                </div>
                <div class="search-result-meta">
                    <div class="search-result-rating">
                        <div class="rating-stars">${generateStars(app.rating, 14)}</div>
                        <span>${app.rating.toFixed(1)}</span>
                        <span>(${formatNumber(app.num)})</span>
                    </div>
                    <span class="search-result-category">${app.type}</span>
                    <span class="search-result-updated">Updated ${formatDate(app.lastReleaseTime)}</span>
                </div>
            </div>
        </div>
    `;
}

// Update results count display
function updateResultsCount() {
    const countElement = document.getElementById('resultsCount');
    const count = currentResults.length;
    
    if (count === 0) {
        countElement.textContent = 'No results found';
    } else if (count === 1) {
        countElement.textContent = '1 result';
    } else {
        countElement.textContent = `${count} results`;
    }
}

// Apply filters and re-search
function applyFilters() {
    // Get filter values
    currentFilters.category = document.getElementById('categoryFilter').value;
    currentFilters.sort = document.getElementById('sortFilter').value;
    currentFilters.minRating = parseFloat(document.getElementById('ratingFilter').value);
    
    // Perform search with new filters
    if (currentQuery) {
        performSearch();
    }
}

// Load more results
function loadMoreResults() {
    if (displayedResults < currentResults.length) {
        displaySearchResults();
        toggleLoadMoreButton();
    }
}

// Toggle load more button visibility
function toggleLoadMoreButton() {
    const container = document.getElementById('loadMoreContainer');
    const button = document.getElementById('loadMoreBtn');
    
    if (displayedResults < currentResults.length) {
        container.style.display = 'block';
        button.disabled = false;
        button.textContent = `Load More Results (${currentResults.length - displayedResults} remaining)`;
    } else {
        container.style.display = 'none';
    }
}

// Clear search
function clearSearch() {
    currentQuery = '';
    currentResults = [];
    displayedResults = 0;
    
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('sortFilter').value = 'relevance';
    document.getElementById('ratingFilter').value = '0';
    
    currentFilters = {
        category: '',
        sort: 'relevance',
        minRating: 0
    };
    
    updateSearchHeader();
    document.getElementById('searchResults').innerHTML = '';
    document.getElementById('noResults').style.display = 'none';
    document.getElementById('loadMoreContainer').style.display = 'none';
    updateResultsCount();
}

// Show no results message
function showNoResults(message = 'No apps found matching your search criteria') {
    const container = document.getElementById('searchResults');
    const noResultsElement = document.getElementById('noResults');
    
    container.innerHTML = '';
    noResultsElement.style.display = 'block';
    
    const titleElement = noResultsElement.querySelector('.no-results-title');
    const textElement = noResultsElement.querySelector('.no-results-text');
    
    if (titleElement) titleElement.textContent = 'No apps found';
    if (textElement) textElement.textContent = message;
}

// Navigate to app details
function navigateToApp(appId) {
    if (appId) {
        window.location.href = `details/detail.html?id=${appId}`;
    }
}

// Handle search form submission
function handleSearch(event) {
    event.preventDefault();
    
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();
    
    if (query) {
        currentQuery = query;
        
        // Update URL without page reload
        const newUrl = new URL(window.location);
        newUrl.searchParams.set('q', query);
        window.history.pushState({}, '', newUrl);
        
        performSearch();
    }
}

// Initialize search suggestions (future enhancement)
function initializeSearchSuggestions() {
    const searchInput = document.getElementById('searchInput');
    
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        if (query.length >= 2) {
            // Could implement search suggestions here
            // For now, we'll just perform the search on Enter or button click
        }
    });
}

// Handle browser back/forward navigation
window.addEventListener('popstate', function(event) {
    const urlParams = new URLSearchParams(window.location.search);
    const query = urlParams.get('q');
    
    if (query !== currentQuery) {
        currentQuery = query || '';
        document.getElementById('searchInput').value = currentQuery;
        
        if (currentQuery) {
            performSearch();
        } else {
            clearSearch();
        }
    }
});

// Initialize search suggestions when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchSuggestions();
});
