/* Common styles for Play Store website */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #202124;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* Header styles */
.header {
    background: #ffffff;
    border-bottom: 1px solid #e8eaed;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 1px 6px rgba(32, 33, 36, 0.1);
}

/* PC端header样式 - 与详情页保持一致 */
@media (min-width: 769px) {
    .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    gap: 24px;
}

/* Logo styles */
.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: inherit;
}

.logo a {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: inherit;
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.logo-text {
    font-size: 20px;
    font-weight: 500;
    color: #1a73e8;
}

/* Navigation styles */
.nav {
    flex: 1;
    max-width: 800px;
    display: flex;
    justify-content: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 8px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

/* Categories section in header */
.categories-section {
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid #e8eaed;
    padding: 12px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.categories-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 16px;
}

.categories-list {
    display: flex;
    gap: 16px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0;
}

.category-item {
    display: flex;
    align-items: center;
}

.category-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    text-decoration: none;
    color: #5f6368;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.2s ease;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.category-link:hover {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.category-link.active {
    background-color: #1a73e8;
    color: white;
}

.category-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* 确保PC端正常显示 */
@media (min-width: 769px) {
    .nav {
        display: flex !important;
    }

    .search-container {
        display: block !important;
    }

    .categories-section {
        display: none !important;
    }

    .mobile-menu-toggle,
    .mobile-search-toggle {
        display: none !important;
    }
}

.nav-link {
    display: block;
    padding: 8px 16px;
    text-decoration: none;
    color: #5f6368;
    font-weight: 500;
    border-radius: 20px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.nav-link:hover {
    background-color: #f8f9fa;
    color: #1a73e8;
}

.nav-link.active {
    background-color: #e8f0fe;
    color: #1a73e8;
}

/* Navigation dropdown */
.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    position: relative;
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 10px;
    margin-left: 6px;
    transition: transform 0.2s ease;
}

.nav-dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    list-style: none;
    padding: 12px 0;
    margin: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 10px 20px;
    color: #5f6368;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
}

.dropdown-link:hover {
    background-color: #f8f9fa;
    color: #1a73e8;
}

/* Search styles */
.search-container {
    min-width: 280px;
}

.search-form {
    display: flex;
    gap: 8px;
    align-items: center;
}

.search-input-wrapper {
    position: relative;
    flex: 1;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #5f6368;
    pointer-events: none;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 40px;
    border: 1px solid #dadce0;
    border-radius: 24px;
    font-size: 14px;
    outline: none;
    transition: all 0.2s ease;
    background-color: #f8f9fa;
}

.search-input:focus {
    border-color: #1a73e8;
    box-shadow: 0 1px 6px rgba(26, 115, 232, 0.2);
    background-color: #ffffff;
}

.search-btn {
    padding: 10px 20px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.search-btn:hover {
    background-color: #1557b0;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
}

/* Mobile menu toggle - 默认隐藏，只在移动端显示 */
.mobile-menu-toggle {
    display: none !important;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.mobile-menu-toggle:hover {
    background-color: rgba(95, 99, 104, 0.1);
}

.mobile-menu-toggle span {
    width: 20px;
    height: 2px;
    background-color: #5f6368;
    transition: all 0.3s ease;
    border-radius: 1px;
}

/* Mobile search toggle - 默认隐藏，只在移动端显示 */
.mobile-search-toggle {
    display: none !important;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    color: #5f6368;
}

.mobile-search-toggle:hover {
    background-color: rgba(95, 99, 104, 0.1);
}

/* Main content */
.main {
    min-height: calc(100vh - 64px - 200px);
    padding: 24px 0;
}

/* Footer styles */
.footer {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 48px 2% 24px;
    margin-top: 48px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 48px;
    margin-bottom: 32px;
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.footer-logo-img {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.footer-logo-text {
    font-size: 18px;
    font-weight: 500;
    color: white;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: white;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 48px;
    height: 48px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background-color: #1557b0;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(26, 115, 232, 0.4);
}

/* Loading indicator */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading.visible {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e8eaed;
    border-top: 4px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading p {
    color: #5f6368;
    font-weight: 500;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-muted {
    color: #5f6368;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.btn-primary {
    background-color: #1a73e8;
    color: white;
}

.btn-primary:hover {
    background-color: #1557b0;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #1a73e8;
    border: 1px solid #dadce0;
}

.btn-secondary:hover {
    background-color: #e8f0fe;
}

.btn-outline {
    background-color: transparent;
    color: #1a73e8;
    border: 1px solid #1a73e8;
}

.btn-outline:hover {
    background-color: #e8f0fe;
}

/* Rating stars */
.rating-stars {
    display: flex;
    gap: 2px;
}

.star {
    width: 16px;
    height: 16px;
    fill: #dadce0;
}

.star.filled {
    fill: #fbbc04;
}

.star.half-filled {
    fill: url(#half-star);
}

/* Mobile search overlay - 在所有屏幕尺寸下都隐藏，只在移动端激活时显示 */
.mobile-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
    display: none;
    flex-direction: column;
}

.mobile-search-overlay.active {
    display: flex;
}

.mobile-search-header {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e8eaed;
    gap: 16px;
}

.mobile-search-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    color: #5f6368;
}

.mobile-search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    padding: 8px 0;
}

/* Mobile menu overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
    display: none;
    flex-direction: column;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.mobile-menu-overlay.active {
    display: flex;
    transform: translateX(0);
}

.mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #e8eaed;
}

.mobile-menu-title {
    font-size: 1.2rem;
    font-weight: 500;
    color: #202124;
}

.mobile-menu-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    color: #5f6368;
}

.mobile-menu-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.mobile-categories-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-category-item {
    margin-bottom: 8px;
}

.mobile-category-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: #5f6368;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.mobile-category-link:hover,
.mobile-category-link.active {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.mobile-search-section {
    padding: 16px;
    border-bottom: 1px solid #e8eaed;
}

.mobile-search-form {
    display: flex;
    gap: 8px;
}

.mobile-search-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #dadce0;
    border-radius: 24px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.2s ease;
}

.mobile-search-input:focus {
    border-color: #1a73e8;
}

.mobile-search-btn {
    padding: 12px 20px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-search-btn:hover {
    background-color: #1557b0;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 20px !important;
}

/* Responsive design */
@media (max-width: 768px) {

    .container {
        padding: 0 12px;
    }

    .header-content {
        height: 56px;
        gap: 12px;
        justify-content: space-between;
    }

    .nav {
        display: none !important;
    }

    .categories-section {
        display: none !important;
    }

    .logo {
        order: 1;
        flex: 1;
        justify-content: flex-start;
    }

    .search-container {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
        order: 2;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .back-to-top {
        bottom: 16px;
        right: 16px;
        width: 44px;
        height: 44px;
    }
}

@media (max-width: 480px) {
    .search-container {
        min-width: 150px;
    }

    .search-input {
        font-size: 16px;
        /* Prevent zoom on iOS */
    }

    .logo-text {
        display: none;
    }
}