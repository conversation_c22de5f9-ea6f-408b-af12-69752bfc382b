{"isGameCenterEnabled": false, "screenshotUrls": [], "ipadScreenshotUrls": [], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/4f/fd/1c/4ffd1cb8-9e68-aa0c-21cf-cecae2720b87/AppIcon-0-0-1x_U007epad-0-1-85-220.png/512x512bb.jpg", "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "kind": "software", "artistViewUrl": "https://apps.apple.com/us/developer/cory-mottice/id1711813865?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/4f/fd/1c/4ffd1cb8-9e68-aa0c-21cf-cecae2720b87/AppIcon-0-0-1x_U007epad-0-1-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/4f/fd/1c/4ffd1cb8-9e68-aa0c-21cf-cecae2720b87/AppIcon-0-0-1x_U007epad-0-1-85-220.png/100x100bb.jpg", "userRatingCountForCurrentVersion": 205, "averageUserRatingForCurrentVersion": 4.88293, "minimumOsVersion": "16.0", "languageCodesISO2A": ["EN"], "fileSizeBytes": "77322240", "formattedPrice": "Free", "trackContentRating": "4+", "artistId": 1711813865, "artistName": "<PERSON>", "genres": ["Weather"], "price": 0, "sellerName": "<PERSON>", "bundleId": "com.corymottice.wiseweather", "releaseDate": "2025-03-03T08:00:00Z", "isVppDeviceBasedLicensingEnabled": true, "trackId": 6736664582, "trackName": "EverythingW<PERSON>her", "currentVersionReleaseDate": "2025-06-18T22:36:23Z", "releaseNotes": "- Special Weather Statements added as a new layer option\n- Tapping a warning in the Active Warnings window will now take you to that warning on the map\n- Improved formatting of AFDs and Tropical Discussions with Key Messages section highlighted\n\nAddition of Tropical Forecast features: \n- Surface wind field\n- Wind radii forecast\n- TS wind Time of Arrival (most likely and earliest reasonable)\n- All probabilistic wind fields\n\n- Fixed Convective Watch outlines to be continuous and not show unrealistic outlines/holes\n- Bug fixes that could hang the screen upon first ever app open when the user chooses not to share location\n- Improved Tropical Watch/Warning lines along the coast\n- Other bug fixes", "version": "1.2.0", "wrapperType": "software", "currency": "USD", "description": "EverythingWeather utilizes the NWS API to get current conditions and forecast data for anywhere within the United States and it's territories. \n\nWhat can be found in the app:\n- Current and forecast conditions with a customizable home page\n- Current hazards (Severe Thunderstorm and Tornado Watches and Warnings) in effect\n- Current SPC and WPC outlooks\n- Real time radar with map overlays (Outlook data for severe weather, fire weather, excessive rainfall, etc.)\n- Storm Reports\n- Tropical Outlooks, Discussions, and Forecasts from the National Hurricane Center.\n- Tropical Cyclone watches and warnings.\n- NWS Products such as Area Forecast Discussion, Climate Reports, Local Storm Reports, etc.\n- Save Favorite locations\n- View detailed weekly and hourly forecast data \n- Sun and Moon data", "averageUserRating": 4.88293, "trackCensoredName": "EverythingW<PERSON>her", "trackViewUrl": "https://apps.apple.com/us/app/everythingweather/id6736664582?uo=4", "contentAdvisoryRating": "4+", "genreIds": ["6001"], "primaryGenreName": "Weather", "primaryGenreId": 6001, "userRatingCount": 205}