// Index page specific JavaScript

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Initialize data
        const dataLoaded = await DataAPI.initializeData();
        if (dataLoaded) {
            await loadHomepageContent();
        } else {
            showErrorMessage('Failed to load app data. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error initializing homepage:', error);
        showErrorMessage('Something went wrong. Please refresh the page.');
    }
});

// Load all homepage content
async function loadHomepageContent() {
    try {
        const featuredApps = DataAPI.getFeaturedApps();

        // Load hero recommended apps
        await loadHeroApps();

        // Load featured swiper first
        await loadFeaturedSwiper();

        // Load each category section with different layouts
        await Promise.all([
            loadBusinessApps(featuredApps.business || []),
            loadWeatherApps(featuredApps.weather || []),
            loadUtilitiesApps(featuredApps.utilities || []),
            loadTravelApps(featuredApps.travel || []),
            loadSportsApps(featuredApps.sports || [])
        ]);

    } catch (error) {
        console.error('Error loading homepage content:', error);
        showErrorMessage('Error loading apps. Please try again.');
    }
}

// Load Hero Recommended Apps
async function loadHeroApps() {
    try {
        const topApps = DataAPI.getTopApps(8);
        const container = document.getElementById('heroApps');

        if (!container || !topApps.length) return;

        container.innerHTML = topApps.map(app => `
            <div class="hero-app-item" onclick="navigateToApp(${app.appId})">
                <img src="${app.icon}" alt="${app.appName}" class="hero-app-icon"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
                <h4 class="hero-app-name">${app.appName}</h4>
                <div class="hero-app-rating">
                    <div class="rating-stars">${generateStars(app.rating, 12)}</div>
                    <span>${app.rating.toFixed(1)}</span>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('Error loading hero apps:', error);
    }
}

// Load Featured Swiper
async function loadFeaturedSwiper() {
    try {
        const topApps = DataAPI.getTopApps(10);
        const container = document.getElementById('featuredSwiperWrapper');

        if (!container || !topApps.length) return;

        container.innerHTML = topApps.map(app => `
            <div class="swiper-slide" onclick="navigateToApp(${app.appId})">
                <div class="featured-app-content">
                    <img src="${app.icon}" alt="${app.appName}" class="featured-app-icon"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
                    <h3 class="featured-app-title">${app.appName}</h3>
                    <p class="featured-app-developer">${app.publisher}</p>
                    <div class="featured-app-rating">
                        <div class="rating-stars">${generateStars(app.rating, 16)}</div>
                        <span>${app.rating.toFixed(1)}</span>
                    </div>
                    <p class="featured-app-description">
                        ${app.subtitle || 'Discover this amazing app and enhance your mobile experience with its powerful features and intuitive design.'}
                    </p>
                </div>
            </div>
        `).join('');

        // Initialize Swiper
        const swiper = new Swiper('.featured-swiper', {
            slidesPerView: 1,
            spaceBetween: 20,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 40,
                }
            }
        });

    } catch (error) {
        console.error('Error loading featured swiper:', error);
    }
}

// Load Business Apps - Featured Grid Layout
async function loadBusinessApps(apps) {
    const container = document.getElementById('business-apps');
    if (!container || !apps.length) return;
    
    const appsToShow = apps.slice(0, 8);
    
    container.innerHTML = appsToShow.map(app => `
        <div class="business-app-card" onclick="navigateToApp(${app.appId})">
            <div class="business-app-header">
                <img src="${app.icon}" alt="${app.appName}" class="business-app-icon" 
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
                <div class="business-app-info">
                    <h3>${app.appName}</h3>
                    <p>${app.publisher}</p>
                </div>
            </div>
            <div class="business-app-rating">
                <div class="rating-stars">${generateStars(app.rating)}</div>
                <span>${app.rating.toFixed(1)}</span>
                <span class="text-muted">(${formatNumber(app.num)})</span>
            </div>
            <div class="business-app-description">
                ${app.subtitle || 'Professional business application for enhanced productivity and efficiency.'}
            </div>
        </div>
    `).join('');
}

// Load Weather Apps - Horizontal Scroll Layout
async function loadWeatherApps(apps) {
    const container = document.getElementById('weather-apps');
    if (!container || !apps.length) return;
    
    const appsToShow = apps.slice(0, 8);
    
    container.innerHTML = appsToShow.map(app => `
        <div class="weather-app-card" onclick="navigateToApp(${app.appId})">
            <img src="${app.icon}" alt="${app.appName}" class="weather-app-icon"
                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
            <h3>${app.appName}</h3>
            <p>${app.publisher}</p>
            <div class="rating-stars">${generateStars(app.rating, 14)}</div>
        </div>
    `).join('');
}

// Load Utilities Apps - Compact List Layout
async function loadUtilitiesApps(apps) {
    const container = document.getElementById('utilities-apps');
    if (!container || !apps.length) return;
    
    const appsToShow = apps.slice(0, 5);
    
    container.innerHTML = appsToShow.map(app => `
        <div class="utilities-app-item" onclick="navigateToApp(${app.appId})">
            <img src="${app.icon}" alt="${app.appName}" class="utilities-app-icon"
                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2Y4ZjlmYSIvPgo8cGF0aCBkPSJNMTIgOGMtMi4yMSAwLTQgMS43OS00IDRzMS43OSA0IDQgNCA0LTEuNzkgNC00LTEuNzktNC00LTR6bTAgNmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6IiBmaWxsPSIjZGFkY2UwIi8+Cjwvc3ZnPgo='">
            <div class="utilities-app-info">
                <h3>${app.appName}</h3>
                <p>${app.publisher}</p>
            </div>
            <div class="utilities-app-rating">
                <div class="rating-stars">${generateStars(app.rating, 14)}</div>
                <span>${app.rating.toFixed(1)}</span>
            </div>
        </div>
    `).join('');
}

// Load Travel Apps - Card Layout
async function loadTravelApps(apps) {
    const container = document.getElementById('travel-apps');
    if (!container || !apps.length) return;
    
    const appsToShow = apps.slice(0, 10);
    console.log(appsToShow.length);
    
    container.innerHTML = appsToShow.map(app => `
        <div class="travel-app-card" onclick="navigateToApp(${app.appId})">
            <div class="travel-app-image-container">
                <img src="${app.icon}" alt="${app.appName}" class="travel-app-image"
                     onerror="this.style.display='none'">
            </div>
            <div class="travel-app-content">
                <h3>${app.appName}</h3>
                <p>${app.publisher}</p>
                <div class="travel-app-footer">
                    <div class="rating-stars">${generateStars(app.rating, 14)}</div>
                    <span class="text-muted">${app.rating.toFixed(1)}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// Load Sports Apps - Banner Layout
async function loadSportsApps(apps) {
    const container = document.getElementById('sports-apps');
    if (!container || !apps.length) return;
    
    const appsToShow = apps.slice(0, 3);
    
    container.innerHTML = appsToShow.map((app, index) => {
        const gradients = [
            'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        ];
        
        return `
            <div class="sports-app-banner" onclick="navigateToApp(${app.appId})" 
                 style="background: ${gradients[index % gradients.length]}">
                <div class="sports-banner-content">
                    <h3>${app.appName}</h3>
                    <p>${app.subtitle || 'Stay active and track your fitness goals with this amazing sports app.'}</p>
                    <div class="sports-banner-footer">
                        <div class="sports-banner-rating">
                            <div class="rating-stars">${generateStars(app.rating, 14)}</div>
                            <span>${app.rating.toFixed(1)}</span>
                        </div>
                        <span class="text-muted">${formatNumber(app.num)} reviews</span>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Navigate to app details page
function navigateToApp(appId) {
    if (appId) {
        window.location.href = `details/detail.html?id=${appId}`;
    }
}

// Show error message
function showErrorMessage(message) {
    const hero = document.querySelector('.hero-content');
    if (hero) {
        hero.innerHTML = `
            <div class="error-container" style="text-align: center; padding: 40px 20px;">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="color: rgba(255,255,255,0.7); margin-bottom: 16px;">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <h2 style="color: rgba(255,255,255,0.9); margin-bottom: 8px; font-weight: 400;">Oops! Something went wrong</h2>
                <p style="color: rgba(255,255,255,0.7); margin-bottom: 20px;">${message}</p>
                <button onclick="window.location.reload()" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: 500;">
                    Refresh Page
                </button>
            </div>
        `;
    }
}

// Handle scroll animations for sections
function initializeScrollAnimations() {
    const sections = document.querySelectorAll('.category-section');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });
}

// Initialize scroll animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeScrollAnimations, 500);
});
