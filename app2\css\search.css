/* Search page specific styles */

/* Search header */
.search-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 24px 0;
}

.search-title {
    font-size: 2rem;
    font-weight: 400;
    color: #202124;
    margin-bottom: 8px;
}

.search-subtitle {
    color: #5f6368;
    font-size: 1rem;
}

/* Search filters */
.search-filters {
    display: flex;
    gap: 16px;
    margin: 0 16px 32px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 150px;
}

.filter-label {
    font-size: 0.85rem;
    font-weight: 500;
    color: #5f6368;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    outline: none;
    transition: border-color 0.2s ease;
}

.filter-select:focus {
    border-color: #1a73e8;
}

/* Search results */
.search-results {
    margin-bottom: 32px;
}

.results-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.results-count {
    color: #fff;
    font-size: 0.9rem;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Search result item */
.search-result-item {
    display: flex;
    gap: 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    margin: 0 16px;
}

.search-result-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.search-result-icon {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    object-fit: cover;
    flex-shrink: 0;
}

.search-result-content {
    flex: 1;
    min-width: 0;
}

.search-result-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: #202124;
    margin-bottom: 4px;
    text-decoration: none;
}

.search-result-title:hover {
    color: #1a73e8;
}

.search-result-developer {
    color: #5f6368;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.search-result-description {
    color: #5f6368;
    font-size: 0.9rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 8px;
}

.search-result-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.search-result-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #5f6368;
    font-size: 0.85rem;
}

.search-result-category {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.search-result-updated {
    color: #5f6368;
    font-size: 0.8rem;
}

/* No results */
.no-results {
    text-align: center;
    padding: 48px 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin: 0 16px;
}

.no-results-icon {
    margin-bottom: 16px;
    color: #dadce0;
}

.no-results-title {
    font-size: 1.2rem;
    font-weight: 400;
    color: #202124;
    margin-bottom: 8px;
}

.no-results-text {
    color: #5f6368;
    margin-bottom: 20px;
}

.clear-search-btn {
    background: #1a73e8;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-search-btn:hover {
    background: #1557b0;
}

/* Load more */
.load-more-container {
    text-align: center;
    margin-top: 32px;
}

.load-more-btn {
    background: white;
    color: #1a73e8;
    border: 1px solid #1a73e8;
    padding: 12px 24px;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.load-more-btn:hover {
    background: #e8f0fe;
}

.load-more-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
    .search-header {
        padding: 16px 0;
        margin-bottom: 24px;
    }
    
    .search-title {
        font-size: 1.6rem;
    }
    
    .search-filters {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
        margin-bottom: 24px;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .search-result-item {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .search-result-icon {
        width: 56px;
        height: 56px;
        align-self: flex-start;
    }
    
    .search-result-meta {
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .search-title {
        font-size: 1.4rem;
    }
    
    .search-result-item {
        padding: 12px;
    }
    
    .search-result-icon {
        width: 48px;
        height: 48px;
    }
    
    .search-result-title {
        font-size: 1rem;
    }
    
    .search-result-description {
        -webkit-line-clamp: 3;
    }
}
