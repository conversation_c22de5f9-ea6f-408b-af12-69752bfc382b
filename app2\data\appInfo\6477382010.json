{"screenshotUrls": [], "ipadScreenshotUrls": [], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/d3/c3/af/d3c3afcf-3199-33f4-f93d-e54985844d4f/AppIcon-0-0-1x_U007ephone-0-1-0-85-220.png/512x512bb.jpg", "isGameCenterEnabled": false, "features": [], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "kind": "software", "artistViewUrl": "https://apps.apple.com/us/developer/visargerd-s-l/id1584764978?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/d3/c3/af/d3c3afcf-3199-33f4-f93d-e54985844d4f/AppIcon-0-0-1x_U007ephone-0-1-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/d3/c3/af/d3c3afcf-3199-33f4-f93d-e54985844d4f/AppIcon-0-0-1x_U007ephone-0-1-0-85-220.png/100x100bb.jpg", "averageUserRatingForCurrentVersion": 4.72003, "userRatingCountForCurrentVersion": 7340, "sellerUrl": "https://imote.app/", "languageCodesISO2A": ["EN", "FR", "DE", "IT", "JA", "PT", "RU", "ES"], "fileSizeBytes": "168939520", "formattedPrice": "Free", "trackContentRating": "4+", "minimumOsVersion": "15", "artistId": 1584764978, "artistName": "VISARGERD, S.L.", "genres": ["Utilities", "Productivity"], "price": 0, "isVppDeviceBasedLicensingEnabled": true, "bundleId": "imote.tv.remote.control", "trackId": 6477382010, "trackName": "Remote Control for TV: iMote", "genreIds": ["6002", "6007"], "sellerName": "VISARGERD, S.L.", "primaryGenreName": "Utilities", "primaryGenreId": 6002, "releaseDate": "2024-11-19T08:00:00Z", "currentVersionReleaseDate": "2025-06-18T06:29:17Z", "releaseNotes": "- Improved device reconnection:\nMore reliable automatic reconnection when reopening the app after closing or minimizing it.\nNew onboarding screens to guide you through the initial device pairing process.\n- Key improvements in stability\n- Bug Fixing and minor corrections\n\nThank you very much for your 5-star ratings :) ＊＊＊＊＊", "version": "1.5.1", "wrapperType": "software", "currency": "USD", "description": "Control your Smart TV with ease using iMote — a feature-packed universal TV remote control app. Compatible with all major TV brands, iMote transforms your smartphone into a powerful universal remote. \n\nSeamlessly adjust the volume, switch inputs, manage apps, or cast media from your device to the TV — all in one place.\n\nDownload iMote now and take full control of your TV experience!\n\n◉ FREE FEATURES:\n\n√ UNIVERSAL REMOTE CONTROL\n\nControl all major Smart TV brands including Samsung, LG, Roku, Vizio, Sony, TCL, Hisense, Philips, Insignia, Sanyo, Sharp, Toshiba, and more.\nSupports popular OS platforms like WebOS (LG), Roku, Android TV / Google TV, Tizen (Samsung), and SmartCast (Vizio).\nEasily access essential controls such as power on/off, navigation buttons, volume adjustment, playback control (play/pause/rewind), and input source switching.\n\n√ SCREEN MIRRORING\n\nStream your phone screen to your TV in real time with zero interruptions. Show off slideshows, enjoy your favorite movies, or dive into mobile games without any delays.\n\n√ CAST MEDIA\n\nStream videos, photos, and music from your phone or tablet directly to your Smart TV. Enhance your viewing experience by casting content seamlessly with just a few taps.\n\n√ INTUITIVE REMOTE UI\n\nEnjoy a user-friendly interface designed to simplify your experience. Navigate through TV functions with ease using a sleek and responsive layout that mimics traditional remotes.\n\n√ APPS SCREEN\n\nQuickly access and manage TV apps directly from your phone. Browse through streaming services or other installed apps on your Smart TV without needing the physical remote.\n\n√ PERSONALIZE SETTINGS\n\nCustomize iMote to suit your preferences. Adjust app settings to create a tailored remote experience that meets your specific needs.\n\n◉ PREMIUM FEATURES:\n\n√ NO ADS\n\nEnjoy an uninterrupted experience with no ads when you upgrade to the premium version of iMote.\n\n√ ADVANCED REMOTE FUNCTIONS\n\nUnlock additional features like advanced playback controls (fast forward/rewind) and customizable button layouts for a more personalized remote experience.\n\n√ UNLIMITED CASTING & MIRRORING\n\nCast & Mirror any media from your device to your Smart TV without restrictions. Enjoy unlimited casting of videos, photos, and music. Enjoy real-time screen mirroring from your device to the TV, completely interruption-free with the premium version of iMote.\n\n√ PRIORITY SUPPORT\n\nGet faster assistance with priority customer support when you upgrade to iMote Premium.\n\nDownload iMote for FREE or upgrade to Premium for full access to all pro features. You can cancel your subscription anytime in the phone settings at least 24 hours before the end of the current subscription period. Subscriptions will automatically renew unless auto-renewal is turned off.\n\nThis application is an independent development, compatible with the mentioned brands or companies, but it is not officially related to or associated with them in any way.\n\n◉ CONTACT:\nFor app support please contact us at:\n<EMAIL>\nTerms of use:\nhttps://imote.app/terms-of-use\nPrivacy policy:\nhttps://imote.app/privacy-policy\n\nTake full control of your Smart TV with iMote — download now and enjoy the convenience of a universal remote right in your pocket!", "trackCensoredName": "Remote Control for TV: iMote", "trackViewUrl": "https://apps.apple.com/us/app/remote-control-for-tv-imote/id6477382010?uo=4", "contentAdvisoryRating": "4+", "averageUserRating": 4.72003, "userRatingCount": 7340}